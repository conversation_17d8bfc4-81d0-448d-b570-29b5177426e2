import React from "react";
import { ConversationListProps } from "../types";
import { Id } from "../../../../../convex/_generated/dataModel";
import ConversationItem from "./ConversationItem";

/**
 * Composant pour la liste des conversations
 */
const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  currentConversationId,
  onSelectConversation,
  onOpenMenu,
  isCollapsed,
  menuOpenId,
  isRenaming,
  newTitle,
  setNewTitle,
  handleRename,
  confirmDelete,
  handleDelete,
  setConfirmDelete,
  renameInputRef,
  hasMoreConversations,
  isLoadingMore,
  onShowMore,
  onScroll,
  conversationListRef
}) => {
  // Si aucune conversation, afficher un message
  if (conversations.length === 0) {
    return (
      <div className={`p-3 ${isCollapsed ? 'text-center' : ''}`}>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {isCollapsed ? "..." : "Aucune conversation"}
        </p>
      </div>
    );
  }

  return (
    <div className={`p-3 ${isCollapsed ? 'flex flex-col items-center' : ''}`}>
      {/* Titre de la section (masqué en mode rétracté) */}
      {!isCollapsed && (
        <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
          Récents
        </h3>
      )}

      {/* Liste des conversations */}
      <div
        className="space-y-1 overflow-y-auto h-[calc(100vh-200px)]"
        ref={conversationListRef}
        onScroll={onScroll}
      >
        {conversations.map((conversation) => (
          <ConversationItem
            key={conversation._id}
            conversation={conversation}
            isSelected={currentConversationId === conversation._id}
            onSelect={() => onSelectConversation(conversation._id)}
            onOpenMenu={(e) => onOpenMenu(e, conversation._id)}
            isRenaming={isRenaming === conversation._id}
            newTitle={newTitle}
            setNewTitle={setNewTitle}
            handleRename={(e) => handleRename(e, conversation._id)}
            confirmDelete={confirmDelete === conversation._id}
            handleDelete={() => handleDelete(conversation._id)}
            setConfirmDelete={(confirm) => setConfirmDelete(confirm ? conversation._id : null)}
            renameInputRef={renameInputRef}
            isCollapsed={isCollapsed}
          />
        ))}

        {/* Bouton "Afficher plus" ou indicateur de chargement */}
        {hasMoreConversations && !isCollapsed && (
          <button
            onClick={onShowMore}
            disabled={isLoadingMore}
            className="w-full mt-2 py-2 px-3 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50"
          >
            {isLoadingMore ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600 dark:text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Chargement...
              </span>
            ) : (
              "Afficher plus"
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default ConversationList;
