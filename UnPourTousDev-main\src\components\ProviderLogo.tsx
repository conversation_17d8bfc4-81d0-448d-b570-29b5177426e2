import React from "react";
import openai<PERSON>ogo from "../assets/icons/color/openai.svg";
import claude<PERSON><PERSON> from "../assets/icons/color/claude.svg";
import cohereLogo from "../assets/icons/color/cohere.svg";
import commandaLogo from "../assets/icons/color/commanda.svg";
import mistral<PERSON>ogo from "../assets/icons/color/mistral.svg";
import geminiLogo from "../assets/icons/color/gemini.svg";
import perplexityLogo from "../assets/icons/color/perplexity.svg";
import llamaLogo from "../assets/icons/color/llava.svg";
import metaLogo from "../assets/icons/color/meta.svg";
import deepseekLogo from "../assets/icons/color/deepseek-color.svg";

// Mapping des noms de fournisseurs aux logos
const providerLogos: Record<string, string> = {
  // Principaux fournisseurs
  openai: openaiLogo,
  anthropic: claude<PERSON><PERSON>,  // Utiliser l'icône Claude pour Anthropic
  claude: claude<PERSON><PERSON>,
  cohere: cohere<PERSON>ogo,
  command: commanda<PERSON><PERSON>,
  mistral: mistral<PERSON><PERSON>,
  "mistral-ai": mistral<PERSON><PERSON>,
  "mistralai": mistralLogo, // Ajout de la version sans tiret
  google: geminiLogo,      // Utiliser l'icône Gemini pour Google
  gemini: geminiLogo,
  perplexity: perplexityLogo,
  meta: metaLogo,
  "meta-llama": metaLogo,
  llama: llamaLogo,
  deepseek: deepseekLogo,

  // Fallback
  default: openaiLogo,
};

interface ProviderLogoProps {
  provider: string;
  size?: number;
  className?: string;
}

/**
 * Composant qui affiche le logo d'un fournisseur de modèle
 *
 * @param provider - Le nom du fournisseur (openai, anthropic, etc.)
 * @param size - La taille du logo en pixels (par défaut: 24)
 * @param className - Classes CSS supplémentaires
 */
export default function ProviderLogo({ provider, size = 24, className = "" }: ProviderLogoProps) {
  // Normaliser le nom du fournisseur (minuscules, sans espaces)
  const normalizedProvider = provider.toLowerCase().trim();

  // Trouver le logo correspondant ou utiliser le logo par défaut
  const logoSrc = providerLogos[normalizedProvider] || providerLogos.default;

  return (
    <img
      src={logoSrc}
      alt={`${provider} logo`}
      width={size}
      height={size}
      className={`rounded-full ${className}`}
    />
  );
}
