import React, { useRef } from "react";
import { MessageInputProps } from "../types";
import ActionButtons from "./ActionButtons";
import CategoryButtons from "./CategoryButtons";
import ModelSelector from "./ModelSelector";
import LargeAutoRouterButton from "./LargeAutoRouterButton";
import { useModelSelection, AUTOSELECT_ID } from "../hooks/useModelSelection";


/**
 * Composant MessageInput pour l'interface Comfy
 * Gère la saisie des messages, l'affichage des boutons de catégorie et la sélection des modèles
 */
const MessageInput: React.FC<MessageInputProps> = ({
  userMessage,
  setUserMessage,
  isLoading,
  handleKeyDown,
  startChat,
  selectedModel,
  setSelectedModel,
  useAutoRouter,
  setUseAutoRouter,
  onStop
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const modelSelectionRef = useRef<HTMLDivElement>(null);

  // Récupérer les fonctions de gestion des catégories depuis le hook useModelSelection
  const {
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Ajuster automatiquement la hauteur du textarea
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const newHeight = Math.min(textareaRef.current.scrollHeight, 300);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  };



  return (
    <div className="p-4 border-t border-claude-light-gray">
      <div className="max-w-4xl mx-auto">
        <div className="relative w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
          <textarea
            ref={textareaRef}
            value={userMessage}
            onChange={(e) => {
              setUserMessage(e.target.value);
              adjustTextareaHeight();
            }}
            onKeyDown={(e) => {
              // Si la touche Entrée est pressée sans Shift, appeler startChat avec activeSelection
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                if (userMessage.trim() && !isLoading) {
                  console.log("[MessageInput.tsx] Touche Entrée pressée, valeur de activeSelection:", activeSelection);
                  startChat(activeSelection);
                }
              } else {
                // Sinon, utiliser le gestionnaire de touches par défaut
                handleKeyDown(e);
              }
            }}
            className="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60 font-body"
            placeholder="Comment puis-je vous aider ?"
            rows={1}
          />
          <div className="flex justify-between px-2 pt-1">
            <div className="flex items-center">
              <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
              </button>
            </div>
            <div className="flex items-center gap-3">
              <div ref={modelSelectionRef} className="text-sm text-gray-400 hidden">
                <select
                  className="bg-claude-gray border-none text-white cursor-pointer p-1 rounded font-body"
                  value={activeSelection}
                  onChange={(e) => handleModelSelect(e.target.value)}
                >
                  <option value={AUTOSELECT_ID}>AutoSelect</option>
                  <option value="anthropic/claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                  <option value="anthropic/claude-3-haiku-20240307">Claude 3 Haiku</option>
                  <option value="anthropic/claude-3-opus-20240229">Claude 3 Opus</option>
                  <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
                  <option value="mistralai/mistral-large-latest">Mistral Large</option>
                </select>
              </div>

              <ActionButtons
                userMessage={userMessage}
                isLoading={isLoading}
                startChat={() => {
                  // Log de diagnostic pour voir la valeur de activeSelection
                  console.log("[MessageInput.tsx] Valeur de activeSelection (depuis useModelSelection) avant appel startChat:", activeSelection);

                  // Construire l'objet d'arguments pour startChat
                  const argumentsPourStartChat = {
                    selectedModelId: activeSelection
                  };

                  console.log("[MessageInput.tsx] Arguments complets passés à startChat:", argumentsPourStartChat);

                  // Appeler startChat avec activeSelection
                  // Nous devons passer activeSelection directement à startChat
                  startChat(activeSelection);
                }}
                onStop={onStop}
              />
            </div>
          </div>
        </div>

        {/* Grand bouton AutoRouter */}
        <div className="mt-4 mb-4">
          <LargeAutoRouterButton
            activeSelection={activeSelection}
            handleAutoSelect={handleAutoSelect}
            isLoading={isLoading}
          />
        </div>

        <CategoryButtons
          openCategory={openCategory}
          toggleCategory={toggleCategory}
        />

        {/* Afficher les modèles si une catégorie est ouverte */}
        {openCategory && (
          <div className="mt-3">
            <ModelSelector
              models={models}
              selectedModel={activeSelection}
              setSelectedModel={handleModelSelect}
              openCategory={openCategory}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageInput;
