import { useEffect, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import autoRouterLogo from "../assets/icons/color/autorouter2.svg";
import logo from "../assets/logo3.svg";
import { AutoSelectLoadingAnimation } from "./common";

interface LoadingMessageProps {
  modelName?: string;
  modelId?: string;
}

export default function LoadingMessage({ modelName, modelId }: LoadingMessageProps) {
  const [dots, setDots] = useState("");
  const [elapsedTime, setElapsedTime] = useState(0);
  const models = useQuery(api.livemodels.list) || [];

  // Déterminer si c'est AutoRouter
  const isAutoRouter = modelId === "openrouter/auto" || modelName === "AutoRouter";

  // Animation des points de chargement
  useEffect(() => {
    const dotsInterval = setInterval(() => {
      setDots((prevDots) => {
        if (prevDots.length >= 3) {
          return "";
        }
        return prevDots + ".";
      });
    }, 500);

    // Compteur de temps écoulé
    const timeInterval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => {
      clearInterval(dotsInterval);
      clearInterval(timeInterval);
    };
  }, []);

  // Formater le temps écoulé
  const formatElapsedTime = () => {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes > 0 ? `${minutes}m ` : ''}${seconds}s`;
  };

  return (
    <div className="rounded-xl p-4 bg-claude-light-gray/45 text-white shadow-md">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="h-8 w-8 flex items-center justify-center">
              {isAutoRouter ? (
                // Animation AutoSelect avec logo qui sautille (écran d'accueil uniquement)
                <AutoSelectLoadingAnimation
                  isLoading={true}
                  logoSrc={logo}
                  size="md"
                />
              ) : (
                // Animation générique pour tous les autres modèles
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"></div>
              )}
            </div>
          </div>
          <div>
            <p className="font-medium whitespace-pre-wrap">
              {isAutoRouter
                ? `AutoSelect réfléchit${dots}`
                : modelName
                ? `${modelName} réfléchit${dots}`
                : `Génération de la réponse${dots}`}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Temps écoulé: {formatElapsedTime()}
            </p>


          </div>
        </div>
    </div>
  );
}
