import { useEffect, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import autoRouterLogo from "../assets/icons/color/autorouter2.svg";

interface LoadingMessageProps {
  modelName?: string;
  modelId?: string;
}

export default function LoadingMessage({ modelName, modelId }: LoadingMessageProps) {
  const [dots, setDots] = useState("");
  const [elapsedTime, setElapsedTime] = useState(0);
  const models = useQuery(api.livemodels.list) || [];

  // Déterminer si le modèle est un modèle de recherche web
  const isWebSearchModel = modelId ?
    models.find(m => m.modelId === modelId)?.webSearch === true :
    false;

  // Déterminer si c'est AutoRouter
  const isAutoRouter = modelId === "openrouter/auto" || modelName === "AutoRouter";

  // Animation des points de chargement
  useEffect(() => {
    const dotsInterval = setInterval(() => {
      setDots((prevDots) => {
        if (prevDots.length >= 3) {
          return "";
        }
        return prevDots + ".";
      });
    }, 500);

    // Compteur de temps écoulé
    const timeInterval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => {
      clearInterval(dotsInterval);
      clearInterval(timeInterval);
    };
  }, []);

  // Formater le temps écoulé
  const formatElapsedTime = () => {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes > 0 ? `${minutes}m ` : ''}${seconds}s`;
  };

  return (
    <div className="rounded-xl p-4 bg-claude-light-gray/45 text-white shadow-md">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="h-8 w-8 flex items-center justify-center">
              {isAutoRouter ? (
                // Logo AutoSelect qui sautille
                <img
                  src={autoRouterLogo}
                  alt="AutoSelect"
                  className="w-7 h-7 animate-bounce"
                />
              ) : (
                // Animation par défaut avec rond bleu
                <>
                  <div className="absolute h-7 w-7 border-2 border-blue-500 dark:border-blue-400 rounded-full pulse-ring"></div>
                  <div className="absolute h-3 w-3 bg-blue-600 dark:bg-blue-500 rounded-full pulse-dot"></div>
                </>
              )}
            </div>
          </div>
          <div>
            <p className="font-medium whitespace-pre-wrap">
              {modelName
                ? `${modelName} réfléchit${dots}`
                : `Génération de la réponse${dots}`}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Temps écoulé: {formatElapsedTime()}
            </p>

            {isWebSearchModel && (
              <div className="mt-2 text-xs bg-tango-100/30 dark:bg-tango-900/30 p-2 rounded-md text-tango-800 dark:text-tango-100 border border-tango-300 dark:border-tango-600/50">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-tango-500 dark:text-tango-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="font-medium">Recherche web en cours</p>
                </div>
                <p className="mt-1 ml-5">Les modèles avec recherche web peuvent prendre plus de temps (1-2 minutes) pour générer une réponse complète avec des sources.</p>
              </div>
            )}
          </div>
        </div>
    </div>
  );
}
