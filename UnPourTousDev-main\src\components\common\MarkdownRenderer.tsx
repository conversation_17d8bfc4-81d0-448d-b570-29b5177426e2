import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
// import rehypeRaw from "rehype-raw"; // Laissé commenté car non nécessaire
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
// import LatexDebugger from "./LatexDebugger"; // Commenté car plus nécessaire
import SafeMermaidRenderer from "./SafeMermaidRenderer";

interface Reference {
  id: string;
  title: string;
  url: string;
}

interface MarkdownRendererProps {
  content: string;
  references?: Reference[];
  isStreaming?: boolean;
  showCursor?: boolean;
}

// Utilisation du composant sécurisé pour les diagrammes Mermaid
const MermaidDiagram = ({ content }: { content: string }) => {
  return <SafeMermaidRenderer content={content} />;
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  references,
  isStreaming = false,
  showCursor = false,
}) => {
  // Fonction pour prétraiter les délimiteurs LaTeX problématiques
  const preprocessLatexDelimiters = (content: string): string => {
    // Remplacer les délimiteurs \[ et \] par des délimiteurs $$ équivalents
    let processedContent = content;

    // Recherche des blocs \[...\] et remplacement par $$...$$
    processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, (_, formula) => {
      return `$$${formula}$$`;
    });

    // Recherche des blocs \(...\) et remplacement par $...$
    processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, (_, formula) => {
      return `$${formula}$`;
    });

    return processedContent;
  };

  // Fonction pour traiter les références dans le contenu
  const processReferences = (content: string): string => {
    if (!references || references.length === 0) {
      return content;
    }

    // Remplacer les références [n] par des liens Markdown
    // tout en conservant le format [n] pour l'affichage
    let processedContent = content;
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(
        refRegex,
        `[[${refNumber}]](${ref.url} "${ref.title}")`
      );
    });

    return processedContent;
  };

  // Fonction pour nettoyer les sauts de ligne répétitifs et problématiques
  const cleanLineBreaks = (content: string): string => {
    return content
      // Supprimer les sauts de ligne multiples (plus de 2 consécutifs)
      .replace(/\n{3,}/g, '\n\n')
      // Supprimer les sauts de ligne après les tirets suivis d'un saut de ligne
      .replace(/(-\s*)\n+/g, '$1')
      // Supprimer les sauts de ligne après les points suivis d'un saut de ligne en début de phrase
      .replace(/(\.\s*)\n+([a-zA-Z])/g, '$1 $2')
      // Supprimer les sauts de ligne isolés après les deux-points
      .replace(/:\s*\n+(-)/g, ':\n$1')
      // Nettoyer les espaces en fin de ligne
      .replace(/[ \t]+$/gm, '')
      // Supprimer les lignes vides contenant seulement des espaces
      .replace(/^\s*$/gm, '');
  };

  // Prétraiter le contenu pour corriger les délimiteurs LaTeX, nettoyer les sauts de ligne, puis traiter les références
  const processedContent = processReferences(preprocessLatexDelimiters(cleanLineBreaks(content)));

  return (
    <>
      {/* Débogueur LaTeX - commenté car plus nécessaire
      {process.env.NODE_ENV === 'development' && (
        <details className="mb-2 text-xs">
          <summary className="cursor-pointer text-gray-500">Déboguer LaTeX</summary>
          <LatexDebugger content={content} />
        </details>
      )}
      */}

      <div className="markdown-content break-words whitespace-pre-wrap text-white">
        <ReactMarkdown
        remarkPlugins={[
          remarkGfm,
          [remarkMath, { singleDollarTextMath: true }]
        ]}
        rehypePlugins={[
          [rehypeKatex, {
            throwOnError: false,
            strict: 'ignore',
            output: 'html', // Changé de 'htmlAndMathml' à 'html'
            trust: false,   // Changé de true à false
            errorColor: '#FF0000', // Changé de 'transparent' à rouge pour le débogage
            macros: {},     // Ajouté pour limiter les macros
            maxSize: 500,   // Ajouté pour limiter la taille des expressions
            maxExpand: 100, // Ajouté pour limiter l'expansion des macros
            delimiters: [
              {left: "$$", right: "$$", display: true},
              {left: "$", right: "$", display: false},
              {left: "\\(", right: "\\)", display: false},
              {left: "\\[", right: "\\]", display: true}
            ]
          }]
          // rehypeRaw // Commenté pour test
        ]}
        components={{
          pre: ({ children }) => (
            <div className="whitespace-pre-wrap break-words">{children}</div>
          ),
          code: ({ className, children, inline, ...props }: any) => {
            const match = /language-(\w+)/.exec(className || '');

            // Gestion spéciale pour les diagrammes Mermaid
            if (match && match[1] === 'mermaid') {
              return (
                <MermaidDiagram content={String(children).replace(/\n$/, '')} />
              );
            }

            return !inline && match ? (
              // Blocs de code avec coloration syntaxique
              <div className="rounded overflow-hidden my-1" style={{ background: 'transparent' }}>
                <SyntaxHighlighter
                  style={vscDarkPlus}
                  language={match[1]}
                  PreTag="div"
                  customStyle={{
                    margin: 0,
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    lineHeight: 1.4,
                    background: '#1e1e1e', // Fond sombre pour le code
                  }}
                  wrapLines={true}
                  wrapLongLines={true}
                  useInlineStyles={true}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              </div>
            ) : (
              // Code en ligne
              <code className="bg-gray-700 text-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>
                {children}
              </code>
            );
          },
          // Personnaliser le rendu des tableaux
          table: ({ children }) => (
            <div className="overflow-x-auto my-2 rounded-lg border border-gray-300 dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">{children}</table>
            </div>
          ),
          // Personnaliser les en-têtes de tableaux
          th: ({ children }) => (
            <th className="px-4 py-2 bg-gray-700 text-left text-xs font-bold text-white uppercase tracking-wider border border-gray-300 dark:border-gray-600">
              {children}
            </th>
          ),
          // Personnaliser les cellules de tableaux
          td: ({ children }) => (
            <td className="px-4 py-2 text-sm text-white border border-gray-300 dark:border-gray-600">
              {children}
            </td>
          ),
          // Paragraphes avec espacement selon les spécifications
          p: ({ children }) => (
            <p style={{ marginTop: '0.5em', marginBottom: '0.5em', lineHeight: 1.4, color: 'white' }}>{children}</p>
          ),
          // Personnaliser les liens pour qu'ils s'ouvrent dans un nouvel onglet
          a: ({ node, href, children, ...props }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
              {...props}
            >
              {children}
            </a>
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
      {isStreaming && showCursor && (
        <span className="animate-pulse">▌</span>
      )}
    </div>
    </>
  );
};

export default MarkdownRenderer;
