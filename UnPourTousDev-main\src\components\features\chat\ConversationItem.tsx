import { ModelIconDisplay } from "../../features/models";
import { Conversation } from "../../../types/Conversation";

interface ConversationItemProps {
  conversation: Conversation & {
    lastModelUsed?: string;
    lastModelProvider?: string;
    usesAutoRouter?: boolean;
  };
}

export default function ConversationItem({ conversation }: ConversationItemProps) {
  return (
    <div className="flex items-center p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
      <ModelIconDisplay conversation={conversation} size={16} className="mr-2" />
      <span className="truncate">{conversation.title}</span>
    </div>
  );
}