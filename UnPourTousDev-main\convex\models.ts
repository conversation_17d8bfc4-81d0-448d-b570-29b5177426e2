import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import {
  isModelWithWebSearch,
  isModelWithStructuredOutput,
  isModelWithReasoning,
  isModelWithStreaming
} from "./utils";

// Types pour les modèles
export interface ModelData {
  name: string;
  modelId: string;
  provider: string;
  description: string;
  enabled: boolean;
  contextLength?: number;
  pricing?: {
    prompt: number;
    completion: number;
  };
  custom?: boolean;
  webSearch?: boolean;
  structuredOutput?: boolean;
  reasoning?: boolean;
  streaming?: boolean;
  chat?: boolean;
}

// Récupère tous les modèles
export const list = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("models").collect();
  },
});

// Récupère tous les modèles (pour l'administration)
export const listAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("models").collect();
  },
});

export const listAvailableModels = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("models")
      .filter((q) => q.eq(q.field("enabled"), true))
      .collect();
  },
});

// Récupère les modèles par fournisseur
export const listByProvider = query({
  args: {
    provider: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("models")
      .filter((q) => q.eq(q.field("provider"), args.provider))
      .collect();
  },
});

// Fonction pour initialiser les modèles par défaut
export const initDefaultModels = mutation({
  args: {},
  handler: async (ctx) => {
    const defaultModels = [
      {
        name: "AutoRouter",
        modelId: "openrouter/auto",
        provider: "openrouter",
        description: "Sélectionne automatiquement le meilleur modèle pour votre requête",
        enabled: true,
        contextLength: 32000,
        pricing: {
          prompt: 0.000005,
          completion: 0.000015,
        },
        chat: true,
        webSearch: true,
        reasoning: true,
        streaming: true,
      },
      {
        name: "GPT-3.5 Turbo",
        modelId: "openai/gpt-3.5-turbo",
        provider: "openai",
        description: "Modèle rapide et efficace pour la plupart des tâches",
        enabled: true,
        contextLength: 16385,
        pricing: {
          prompt: 0.0000005,
          completion: 0.0000015,
        },
      },
      {
        name: "GPT-4 Turbo",
        modelId: "openai/gpt-4-turbo-preview",
        provider: "openai",
        description: "Version améliorée de GPT-4, plus rapide et avec des connaissances plus récentes",
        enabled: true,
        contextLength: 128000,
        pricing: {
          prompt: 0.000005,
          completion: 0.000015,
        },
      },
      {
        name: "Claude 3 Opus",
        modelId: "anthropic/claude-3-opus",
        provider: "anthropic",
        description: "Le modèle le plus puissant d'Anthropic, excellent pour les tâches complexes",
        enabled: true,
        contextLength: 200000,
        pricing: {
          prompt: 0.000015,
          completion: 0.000075,
        },
      },
      {
        name: "Claude 3 Sonnet",
        modelId: "anthropic/claude-3-sonnet",
        provider: "anthropic",
        description: "Bon équilibre entre performances et coût",
        enabled: true,
        contextLength: 200000,
        pricing: {
          prompt: 0.000003,
          completion: 0.000015,
        },
      },
    ];

    // Vérifie si des modèles existent déjà
    const existingModels = await ctx.db.query("models").collect();

    if (existingModels.length === 0) {
      // Insère les modèles par défaut
      for (const model of defaultModels) {
        await ctx.db.insert("models", model);
      }
    }
  },
});

// Fonction pour activer/désactiver un modèle
export const toggleModelStatus = mutation({
  args: {
    modelId: v.id("models"),
    enabled: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.modelId, { enabled: args.enabled });
    return { success: true };
  },
});

// Fonction pour activer/désactiver plusieurs modèles en une seule opération
export const toggleMultipleModels = mutation({
  args: {
    modelIds: v.array(v.id("models")),
    enabled: v.boolean(),
    provider: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let count = 0;

    // Si des IDs spécifiques sont fournis, mettre à jour ces modèles
    if (args.modelIds.length > 0) {
      for (const modelId of args.modelIds) {
        await ctx.db.patch(modelId, { enabled: args.enabled });
        count++;
      }
    }
    // Sinon, si un fournisseur est spécifié, mettre à jour tous les modèles de ce fournisseur
    else if (args.provider) {
      const modelsToUpdate = await ctx.db
        .query("models")
        .filter((q) => q.eq(q.field("provider"), args.provider))
        .collect();

      for (const model of modelsToUpdate) {
        await ctx.db.patch(model._id, { enabled: args.enabled });
        count++;
      }
    }
    // Sinon, mettre à jour tous les modèles
    else {
      const allModels = await ctx.db.query("models").collect();

      for (const model of allModels) {
        await ctx.db.patch(model._id, { enabled: args.enabled });
        count++;
      }
    }

    return { success: true, count };
  },
});

// Fonction pour ajouter un modèle personnalisé
export const addCustomModel = mutation({
  args: {
    name: v.string(),
    modelId: v.string(),
    provider: v.string(),
    description: v.string(),
    contextLength: v.optional(v.number()),
    promptPrice: v.optional(v.number()),
    completionPrice: v.optional(v.number()),
    webSearch: v.optional(v.boolean()),
    structuredOutput: v.optional(v.boolean()),
    reasoning: v.optional(v.boolean()),
    streaming: v.optional(v.boolean()),
    chat: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Vérifie si le modèle existe déjà
    const existingModel = await ctx.db
      .query("models")
      .filter((q) => q.eq(q.field("modelId"), args.modelId))
      .first();

    if (existingModel) {
      throw new Error(`Un modèle avec l'ID ${args.modelId} existe déjà`);
    }

    // Crée le nouveau modèle
    const modelData: ModelData = {
      name: args.name,
      modelId: args.modelId,
      provider: args.provider,
      description: args.description,
      enabled: true,
      custom: true,
    };

    // Ajoute les champs optionnels s'ils sont fournis
    if (args.contextLength) {
      modelData.contextLength = args.contextLength;
    }

    if (args.promptPrice !== undefined || args.completionPrice !== undefined) {
      modelData.pricing = {
        prompt: args.promptPrice || 0,
        completion: args.completionPrice || 0,
      };
    }

    // Ajoute les capacités du modèle si elles sont fournies
    if (args.webSearch !== undefined) {
      modelData.webSearch = args.webSearch;
    }

    if (args.structuredOutput !== undefined) {
      modelData.structuredOutput = args.structuredOutput;
    }

    if (args.reasoning !== undefined) {
      modelData.reasoning = args.reasoning;
    }

    if (args.streaming !== undefined) {
      modelData.streaming = args.streaming;
    }

    if (args.chat !== undefined) {
      modelData.chat = args.chat;
    }

    // Insère le modèle dans la base de données
    const modelId = await ctx.db.insert("models", modelData);

    return { success: true, modelId };
  },
});

// Fonction pour mettre à jour un modèle existant
export const updateModel = mutation({
  args: {
    modelId: v.id("models"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    contextLength: v.optional(v.number()),
    promptPrice: v.optional(v.number()),
    completionPrice: v.optional(v.number()),
    enabled: v.optional(v.boolean()),
    webSearch: v.optional(v.boolean()),
    structuredOutput: v.optional(v.boolean()),
    reasoning: v.optional(v.boolean()),
    streaming: v.optional(v.boolean()),
    chat: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { modelId, promptPrice, completionPrice, ...otherArgs } = args;

    // Prépare les données à mettre à jour
    const updateData: any = { ...otherArgs };

    // Met à jour les prix si fournis
    if (promptPrice !== undefined || completionPrice !== undefined) {
      // Récupère le modèle actuel pour conserver les valeurs existantes si non fournies
      const currentModel = await ctx.db.get(modelId);

      updateData.pricing = {
        prompt: promptPrice ?? currentModel?.pricing?.prompt ?? 0,
        completion: completionPrice ?? currentModel?.pricing?.completion ?? 0,
      };
    }

    // Met à jour le modèle
    await ctx.db.patch(modelId, updateData);

    return { success: true };
  },
});

// Fonction pour supprimer un modèle
export const deleteModel = mutation({
  args: {
    modelId: v.id("models"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.modelId);
    return { success: true };
  },
});

// Fonction pour synchroniser les modèles depuis OpenRouter
export const syncModelsFromOpenRouter = mutation({
  args: {
    models: v.array(
      v.object({
        name: v.string(),
        modelId: v.string(),
        provider: v.string(),
        description: v.string(),
        enabled: v.boolean(),
        contextLength: v.optional(v.number()),
        pricing: v.optional(
          v.object({
            prompt: v.number(),
            completion: v.number(),
          })
        ),
        webSearch: v.optional(v.boolean()),
        structuredOutput: v.optional(v.boolean()),
        reasoning: v.optional(v.boolean()),
        streaming: v.optional(v.boolean()),
        chat: v.optional(v.boolean()),
      })
    ),
  },
  handler: async (ctx, args) => {
    // Récupère les modèles existants
    const existingModels = await ctx.db.query("models").collect();

    // Crée un map des modèles existants pour faciliter la recherche
    const existingModelsMap = new Map();
    existingModels.forEach(model => {
      existingModelsMap.set(model.modelId, model);
    });

    // Compteurs pour les statistiques
    let added = 0;
    let updated = 0;

    // Traite chaque modèle
    for (const model of args.models) {
      const existingModel = existingModelsMap.get(model.modelId);

      // Détermine les capacités du modèle
      const isWebSearch = isModelWithWebSearch(model.modelId);
      const isStructuredOutput = isModelWithStructuredOutput(model.modelId);
      const isReasoning = isModelWithReasoning(model.modelId);
      const isStreaming = isModelWithStreaming(model.modelId);

      // Détermine si le modèle est principalement pour le chat
      const isChat = !isWebSearch && !isReasoning;

      if (existingModel) {
        // Met à jour le modèle existant, mais préserve son état d'activation
        await ctx.db.patch(existingModel._id, {
          name: model.name,
          description: model.description,
          contextLength: model.contextLength,
          pricing: model.pricing,
          webSearch: isWebSearch,
          structuredOutput: isStructuredOutput,
          reasoning: isReasoning,
          streaming: isStreaming,
          chat: model.chat !== undefined ? model.chat : isChat,
          // Ne pas mettre à jour le champ enabled pour préserver l'état actuel
        });
        updated++;
      } else {
        // Ajoute un nouveau modèle (désactivé par défaut)
        await ctx.db.insert("models", {
          ...model,
          custom: false,
          webSearch: isWebSearch,
          structuredOutput: isStructuredOutput,
          reasoning: isReasoning,
          streaming: isStreaming,
          chat: model.chat !== undefined ? model.chat : isChat,
          // Le champ enabled est déjà défini à false dans la fonction syncModels
        });
        added++;
      }
    }

    return { success: true, added, updated };
  },
});

// Fonction pour mettre à jour les propriétés des capacités pour tous les modèles
export const updateModelCapabilities = mutation({
  args: {},
  handler: async (ctx) => {
    // Récupère tous les modèles
    const models = await ctx.db.query("models").collect();
    let updated = 0;

    // Met à jour chaque modèle
    for (const model of models) {
      const isWebSearch = isModelWithWebSearch(model.modelId);
      const isStructuredOutput = isModelWithStructuredOutput(model.modelId);
      const isReasoning = isModelWithReasoning(model.modelId);
      const isStreaming = isModelWithStreaming(model.modelId);

      // Vérifie si l'une des propriétés a changé
      const hasChanged =
        model.webSearch !== isWebSearch ||
        model.structuredOutput !== isStructuredOutput ||
        model.reasoning !== isReasoning ||
        model.streaming !== isStreaming;

      // Met à jour seulement si au moins une propriété est différente
      if (hasChanged) {
        await ctx.db.patch(model._id, {
          webSearch: isWebSearch,
          structuredOutput: isStructuredOutput,
          reasoning: isReasoning,
          streaming: isStreaming,
        });
        updated++;
      }
    }

    return { success: true, updated };
  },
});

// Fonction pour envoyer les modèles activés vers la table livemodels
export const sendModelsToLive = mutation({
  args: {},
  handler: async (ctx) => {
    // Récupère tous les modèles activés
    const enabledModels = await ctx.db
      .query("models")
      .filter((q) => q.eq(q.field("enabled"), true))
      .collect();

    // Supprime tous les modèles existants dans livemodels
    const existingLiveModels = await ctx.db.query("livemodels").collect();
    for (const model of existingLiveModels) {
      await ctx.db.delete(model._id);
    }

    // Ajoute les modèles activés à la table livemodels
    let addedCount = 0;
    for (const model of enabledModels) {
      // Crée une copie du modèle sans les champs enabled et _id
      const { enabled, _id, _creationTime, ...modelData } = model;

      // Ajoute le modèle à la table livemodels
      await ctx.db.insert("livemodels", modelData);
      addedCount++;
    }

    return {
      success: true,
      count: addedCount,
      message: `${addedCount} modèles envoyés en production`
    };
  },
});