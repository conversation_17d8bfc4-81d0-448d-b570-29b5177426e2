import React from "react";
import { NewConversationFormProps } from "./types";
import { useModelSelection } from "./hooks/useModelSelection";
import { useMessageInput } from "./hooks/useMessageInput";
import MessageTextarea from "./components/MessageTextarea";
import ModelSelector from "./components/ModelSelector";
// Importer la version avec curseur pour la page de nouvelle conversation
import { SpotlightBoxCursor } from "../../../common";
import LogoNewConversation from "../../../../assets/LogoNewConversation.png";

/**
 * Formulaire pour créer une nouvelle conversation
 */
const NewConversationForm: React.FC<NewConversationFormProps> = ({
  onConversationCreated
}) => {
  // Utiliser les hooks personnalisés
  const {
    selectedModel,
    setSelectedModel,
    openCategory,
    toggleCategory,
    models
  } = useModelSelection();

  const {
    message,
    setMessage,
    isSubmitting,
    handleSubmit
  } = useMessageInput(onConversationCreated, selectedModel);

  return (
    <div className="max-w-2xl w-full mx-auto px-4">
      <div className="flex justify-center" style={{ marginBottom: "-1.5rem" }}>
        <img
          src={LogoNewConversation}
          alt="Logo UnPourTous"
          className="h-[14.3rem] object-contain relative z-10"
        />
      </div>
      <SpotlightBoxCursor>
        <form
          onSubmit={(e) => {
            // Ne soumettre le formulaire que si le bouton d'envoi est cliqué ou la touche Entrée est pressée
            if ((e.nativeEvent as SubmitEvent).submitter) {
              handleSubmit(e);
            } else {
              e.preventDefault();
            }
          }}
          className="relative">
          {/* Zone de texte pour le message */}
          <MessageTextarea
            message={message}
            setMessage={setMessage}
            isSubmitting={isSubmitting}
          />

          {/* Sélecteur de modèles */}
          <ModelSelector
            models={models}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            openCategory={openCategory}
            toggleCategory={toggleCategory}
          />
        </form>
      </SpotlightBoxCursor>
    </div>
  );
};

export default NewConversationForm;





