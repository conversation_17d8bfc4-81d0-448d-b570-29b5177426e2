import React from "react";
import { SourcesListProps } from "../types";
import { SourceTile, MoreSourcesTile } from "../../../../common";

/**
 * Composant pour afficher la liste des sources
 */
const SourcesList: React.FC<SourcesListProps> = ({
  references,
  showAllSources,
  setShowAllSources
}) => {
  if (!references || references.length === 0) {
    return null;
  }

  return (
    <div className="mb-3 pt-1 pb-2 border-b border-gray-300 dark:border-gray-600">
      <h4 className="text-xs font-medium mb-1 text-gray-700 dark:text-gray-300">Sources:</h4>
      <div className="sources-row flex flex-wrap w-full gap-1">
        {/* Affichage des 3 premières sources (28% chacune) */}
        {references.slice(0, Math.min(3, references.length)).map((ref) => (
          <div key={ref.id} className="flex-[2.8] min-w-0">
            <SourceTile source={ref} />
          </div>
        ))}

        {/* Affichage de la tuile "+X sources" si plus de 3 sources (16%) */}
        {references.length > 3 && (
          <div className="flex-[1.6] min-w-0">
            <MoreSourcesTile
              sources={references.slice(3)}
              onClick={() => setShowAllSources(!showAllSources)}
            />
          </div>
        )}

        {/* Remplir l'espace si moins de 3 sources */}
        {references.length === 1 && (
          <div className="flex-[5.6]"></div>
        )}
        {references.length === 2 && (
          <div className="flex-[2.8]"></div>
        )}
      </div>

      {/* Affichage de toutes les sources si showAllSources est true */}
      {showAllSources && (
        <div className="mt-3 pt-3 sources-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {references.slice(3).map((ref) => (
            <div key={ref.id}>
              <SourceTile source={ref} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SourcesList;
