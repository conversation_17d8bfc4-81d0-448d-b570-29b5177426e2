import React from 'react';
import MarkdownRenderer from '../components/common/MarkdownRenderer';

/**
 * Composant de test pour le MarkdownRenderer
 */
const MarkdownTest: React.FC = () => {
  // Exemple de contenu Markdown avec différents éléments
  const markdownContent = `
# Test du MarkdownRenderer

## Tableaux

Voici un tableau simple :

| Catégorie | Élément | Statut    | Priorité |
| :-------- | :------ | :-------- | :------- |
| Tâche A   | Design  | En cours  | Haute    |
| Tâche B   | Dev     | À faire   | Moyenne  |
| Tâche C   | Test    | Terminé   | Basse    |
| Tâche D   | Doc     | En attente| Moyenne  |

## Code

Voici un exemple de code :

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

## Listes

- Élément 1
- Élément 2
  - Sous-élément 2.1
  - Sous-élément 2.2
- Élément 3

1. Premier
2. Deuxième
3. Troisième
`;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold text-center mb-8">Test du MarkdownRenderer</h1>
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <MarkdownRenderer
          content={markdownContent}
          isStreaming={false}
          showCursor={false}
        />
      </div>
    </div>
  );
};

export default MarkdownTest;
