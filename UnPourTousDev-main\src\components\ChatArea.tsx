import { useEffect, useRef, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import LoadingMessage from "./LoadingMessage";
import UserMessage from "./UserMessage";
import AiMessage from "./AiMessage";
import NewConversationForm from "./features/chat/NewConversationForm";

interface ChatAreaProps {
  conversationId: Id<"conversations"> | null;
  isNewConversation?: boolean;
  onSelectConversation?: (id: Id<"conversations">) => void;
}

export default function ChatArea({ conversationId, isNewConversation = false, onSelectConversation }: ChatAreaProps) {
  const messages = useQuery(api.messages.list, conversationId ? { conversationId } : "skip") || [];
  const bottomRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  // Défilement automatique vers le bas
  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Détecte si l'IA est en train de générer une réponse
  useEffect(() => {
    if (!conversationId || messages.length === 0) return;

    const lastMessage = messages[messages.length - 1];

    // Si le dernier message est de l'utilisateur, l'IA est en train de générer une réponse
    if (lastMessage.role === "user") {
      setIsLoading(true);
      setSelectedModel(lastMessage.modelUsed);
    } else {
      setIsLoading(false);
      setSelectedModel(null);
    }
  }, [conversationId, messages]);

  // Récupère le nom du modèle à partir de son ID
  const models = useQuery(api.livemodels.list) || [];
  const getModelName = (modelId: string) => {
    const model = models.find(m => m.modelId === modelId);
    return model ? model.name : modelId;
  };

  if (!conversationId && !isNewConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-surface-light dark:bg-surface-dark transition-colors duration-200">
        <p className="text-gray-500 dark:text-gray-400">
          Sélectionnez une conversation ou commencez-en une nouvelle
        </p>
      </div>
    );
  }

  if (isNewConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-surface-light dark:bg-surface-dark transition-colors duration-200">
        {onSelectConversation ? (
          <NewConversationForm onConversationCreated={onSelectConversation} />
        ) : (
          <div className="text-center max-w-md px-4">
            <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-gray-200">Nouvelle conversation</h2>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Sélectionnez un modèle et envoyez un message pour commencer une nouvelle conversation.
            </p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-surface-light dark:bg-surface-dark p-4 transition-colors duration-200">
      <div className="max-w-4xl mx-auto space-y-4">
        {messages.map((message) => (
          message.role === "user" ? (
            <UserMessage key={message._id} message={message} />
          ) : (
            <AiMessage key={message._id} message={message} />
          )
        ))}

        {/* Indicateur de chargement amélioré */}
        {isLoading && selectedModel && (
          <LoadingMessage
            modelName={getModelName(selectedModel)}
            modelId={selectedModel}
          />
        )}

        <div ref={bottomRef} />
      </div>
    </div>
  );
}
