import React from "react";
import { CategoryButtonsProps } from "../types";

/**
 * Composant CategoryButtons pour l'interface Comfy
 * Affiche les trois catégories principales de modèles:
 * - Chat (pour les modèles de conversation générale)
 * - Recherche Web (pour les modèles avec capacité de recherche)
 * - Raisonnement (pour les modèles optimisés pour la résolution de problèmes)
 */
const CategoryButtons: React.FC<CategoryButtonsProps> = ({
  openCategory,
  toggleCategory
}) => {
  return (
    <div className="flex justify-center mt-4 flex-wrap gap-3 font-body">
      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "chat"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
        text-sm flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all`}
        onClick={() => toggleCategory("chat")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
        </svg>
        Discussion
      </button>

      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "web_search"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
        text-sm flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all`}
        onClick={() => toggleCategory("web_search")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        </svg>
        Recherche Web
      </button>

      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "reasoning"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
        text-sm flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all`}
        onClick={() => toggleCategory("reasoning")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
        </svg>
        Raisonnement
      </button>
    </div>
  );
};

export default CategoryButtons;
