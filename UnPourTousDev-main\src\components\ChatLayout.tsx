import { useState } from "react";
import { Id } from "../../convex/_generated/dataModel";
import ChatArea from "./ChatArea";
import MessageInput from "./MessageInput";
import Sidebar from "./layout/Sidebar/Sidebar";

export default function ChatLayout() {
  // Utilise null pour une conversation existante, et "new" pour une conversation temporaire
  // Initialise à "new" pour afficher la page de nouvelle conversation par défaut
  const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null | "new">("new");

  // Fonction pour créer une nouvelle conversation temporaire
  const handleNewConversation = () => {
    setCurrentConversationId("new");
  };

  // Fonction pour sélectionner une conversation existante
  const handleSelectConversation = (id: Id<"conversations">) => {
    setCurrentConversationId(id);
  };

  return (
    <div className="flex h-screen bg-surface-light dark:bg-surface-dark transition-colors duration-200">
      <Sidebar
        currentConversationId={currentConversationId}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
      />
      <div className="flex-1 flex flex-col">
        <ChatArea
          conversationId={currentConversationId === "new" ? null : currentConversationId}
          isNewConversation={currentConversationId === "new"}
          onSelectConversation={handleSelectConversation}
        />
        {currentConversationId !== "new" && (
          <MessageInput
            conversationId={currentConversationId}
            onNewConversation={setCurrentConversationId}
            isNewConversation={false}
          />
        )}
      </div>
    </div>
  );
}
