import React, { useEffect } from "react";
import { Navigate } from "react-router-dom";
import useInterfacePreference from "../../../hooks/useInterfacePreference";

/**
 * Composant pour rediriger vers l'interface préférée
 */
const InterfaceRedirect: React.FC = () => {
  const { interfaceType, isLoading } = useInterfacePreference();

  // Si les préférences sont en cours de chargement, afficher un spinner
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-claude-dark">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }
  
  // Si aucune préférence n'est définie, rediriger vers la page de sélection
  if (!interfaceType) {
    return <Navigate to="/interface-selector" replace />;
  }
  
  // Rediriger vers l'interface préférée
  if (interfaceType === "claude") {
    return <Navigate to="/claude" replace />;
  } else {
    return <Navigate to="/original" replace />;
  }
};

export default InterfaceRedirect;
