import { Id } from "../../../../convex/_generated/dataModel";

/**
 * Interface pour les propriétés du composant Sidebar
 */
export interface SidebarProps {
  currentConversationId: Id<"conversations"> | null | "new";
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void;
}

/**
 * Interface pour les propriétés du composant SidebarHeader
 */
export interface SidebarHeaderProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

/**
 * Interface pour les propriétés du composant SidebarFooter
 */
export interface SidebarFooterProps {
  isCollapsed: boolean;
  onOpenSettingsMenu: (e: React.MouseEvent) => void;
  settingsMenuOpen: boolean;
}

/**
 * Interface pour les propriétés du composant ConversationList
 */
export interface ConversationListProps {
  conversations: any[];
  currentConversationId: Id<"conversations"> | null | "new";
  onSelectConversation: (id: Id<"conversations">) => void;
  onOpenMenu: (e: React.MouseEvent, conversationId: Id<"conversations">) => void;
  isCollapsed: boolean;
  menuOpenId: Id<"conversations"> | null;
  isRenaming: Id<"conversations"> | null;
  newTitle: string;
  setNewTitle: (title: string) => void;
  handleRename: (e: React.FormEvent, conversationId: Id<"conversations">) => void;
  confirmDelete: Id<"conversations"> | null;
  handleDelete: (conversationId: Id<"conversations">) => void;
  setConfirmDelete: (id: Id<"conversations"> | null) => void;
  renameInputRef: React.RefObject<HTMLInputElement | null>;
  hasMoreConversations?: boolean;
  isLoadingMore?: boolean;
  onShowMore?: () => void;
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
  conversationListRef?: React.RefObject<HTMLDivElement | null>;
}



/**
 * Interface pour les propriétés du composant SidebarActions
 */
export interface SidebarActionsProps {
  onNewConversation: () => void;
  isCollapsed: boolean;
}

/**
 * Interface pour les propriétés du composant ContextMenus
 */
export interface ContextMenusProps {
  menuOpen: Id<"conversations"> | null;
  settingsMenuOpen: boolean;
  menuPosition: { x: number, y: number, openUpward: boolean };
  handleStartRename: (e: React.MouseEvent, conversationId: Id<"conversations">, title: string) => void;
  handleConfirmDelete: (e: React.MouseEvent, conversationId: Id<"conversations">) => void;
  conversations: any[];
  handleSignOut: () => void;
  onCloseMenu?: () => void;
  onCloseSettingsMenu?: () => void;
}
