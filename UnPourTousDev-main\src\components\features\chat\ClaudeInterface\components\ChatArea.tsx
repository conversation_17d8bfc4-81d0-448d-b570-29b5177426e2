import React from "react";
import { ChatAreaProps } from "../types";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import AiMessage from "../../AiMessage/AiMessage";
import UserMessage from "../../UserMessage/UserMessage";

/**
 * Composant ChatArea pour l'interface Claude
 */
const ChatArea: React.FC<ChatAreaProps> = ({
  chatStarted,
  conversationId,
  userMessage,
  assistantResponse,
  isLoading,
  renderWelcomeScreen,
  renderChatScreen
}) => {
  // Récupérer les messages de la conversation depuis Convex
  const messages = useQuery(
    api.messages.list,
    conversationId ? { conversationId } : "skip"
  ) || [];

  // Afficher l'écran de bienvenue si la conversation n'a pas commencé
  if (!chatStarted) {
    return renderWelcomeScreen();
  }

  // Si nous avons un ID de conversation, afficher les messages de la conversation
  if (conversationId && messages.length > 0) {
    return (
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {messages.map((message) => (
          <div key={message._id} className="max-w-4xl mx-auto">
            {message.role === "user" ? (
              <UserMessage message={message} />
            ) : (
              <AiMessage message={message} />
            )}
          </div>
        ))}
        {isLoading && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-claude-light-gray/45 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
              <div className="text-white font-body">
                <span className="animate-pulse">...</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Sinon, afficher l'écran de chat simulé
  return renderChatScreen();
};

export default ChatArea;
