import React from "react";

// Importation des icônes en couleur
import openaiColorLogo from "../../../assets/icons/color/openai.svg";
import openaiWhiteLogo from "../../../assets/icons/white/openai.svg"; // Logo blanc d'OpenAI
import claude<PERSON>ogo from "../../../assets/icons/color/claude.svg";
import mistralLogo from "../../../assets/icons/color/mistral.svg";
import geminiLogo from "../../../assets/icons/color/gemini.svg";
import cohereCommandLogo from "../../../assets/icons/color/commanda.svg";
import cohereLogo from "../../../assets/icons/color/cohere.svg";
import perplexityLogo from "../../../assets/icons/color/perplexity.svg";
import llamaLogo from "../../../assets/icons/color/llava.svg";
import metaLogo from "../../../assets/icons/color/meta.svg";
import deepseekLogo from "../../../assets/icons/color/deepseek-color.svg";
import grokLogo from "../../../assets/icons/color/grok.svg";
import autoRouterLogo from "../../../assets/icons/color/autorouter2.svg";
//import autoRouterLogo from "../../../assets/icons/color/autorouter.png";

// Mapping des modèles aux logos
const modelIcons: Record<string, string> = {
  // OpenRouter
  "openrouter/auto": autoRouterLogo,

  // OpenAI (utiliser le logo blanc)
  "gpt-4": openaiWhiteLogo,
  "gpt-4-turbo": openaiWhiteLogo,
  "gpt-3.5-turbo": openaiWhiteLogo,
  "openai/gpt-4": openaiWhiteLogo,
  "openai/gpt-4-turbo": openaiWhiteLogo,
  "openai/gpt-3.5-turbo": openaiWhiteLogo,

  // Anthropic
  "claude-3-opus": claudeLogo,
  "claude-3-sonnet": claudeLogo,
  "claude-3-haiku": claudeLogo,
  "claude-2": claudeLogo,
  "anthropic/claude-3-opus": claudeLogo,
  "anthropic/claude-3-sonnet": claudeLogo,
  "anthropic/claude-3-haiku": claudeLogo,
  "anthropic/claude-2": claudeLogo,

  // Google
  "gemini-pro": geminiLogo,
  "gemini-1.5-pro": geminiLogo,
  "google/gemini-pro": geminiLogo,
  "google/gemini-1.5-pro": geminiLogo,

  // Mistral
  "mistral-small": mistralLogo,
  "mistral-medium": mistralLogo,
  "mistral-large": mistralLogo,
  "mistral-ai/mistral-small": mistralLogo,
  "mistral-ai/mistral-medium": mistralLogo,
  "mistral-ai/mistral-large": mistralLogo,

  // Cohere
  "command": cohereCommandLogo,
  "command-r": cohereCommandLogo,
  "cohere/command": cohereCommandLogo,
  "cohere/command-r": cohereCommandLogo,

  // Perplexity
  "sonar": perplexityLogo,
  "perplexity/sonar": perplexityLogo,

  // Meta
  "llama-3": llamaLogo,
  "meta-llama/llama-3-70b-instruct": llamaLogo,
  "meta-llama/llama-3-8b-instruct": llamaLogo,

  // xAI
  "grok-1": grokLogo,
  "grok-1.5": grokLogo,
  "grok-2": grokLogo,
  "grok-3": grokLogo,
  "xai/grok-1": grokLogo,
  "xai/grok-1.5": grokLogo,
  "xai/grok-2": grokLogo,
};

// Mapping des fournisseurs aux logos
const providerIcons: Record<string, string> = {
  "openai": openaiWhiteLogo, // Utiliser le logo blanc pour OpenAI
  "anthropic": claudeLogo,   // Utiliser l'icône Claude pour Anthropic
  "claude": claudeLogo,
  "google": geminiLogo,      // Utiliser l'icône Gemini pour Google
  "gemini": geminiLogo,
  "mistral": mistralLogo,
  "mistral-ai": mistralLogo,
  "mistralai": mistralLogo, // Ajout de la version sans tiret
  "cohere": cohereLogo,
  "command": cohereCommandLogo,
  "perplexity": perplexityLogo,
  "meta": metaLogo,
  "meta-llama": metaLogo,
  "llama": llamaLogo,
  "deepseek": deepseekLogo,
  "openrouter": autoRouterLogo, // Utiliser notre icône personnalisée pour OpenRouter

  // xAI
  "grok": grokLogo,
  "xai": grokLogo,
  "x.ai": grokLogo,
  "x-ai": grokLogo,
};

interface ModelIconProps {
  model: string;
  provider?: string;
  size?: number;
  className?: string;
}

/**
 * Composant qui affiche l'icône d'un modèle d'IA
 *
 * @param model - L'identifiant du modèle (ex: "gpt-4", "claude-3-sonnet")
 * @param provider - Le fournisseur du modèle (optionnel, utilisé si le modèle n'est pas trouvé)
 * @param size - La taille de l'icône en pixels (par défaut: 24)
 * @param className - Classes CSS supplémentaires
 */
export default function ModelIcon({
  model,
  provider,
  size = 24,
  className = ""
}: ModelIconProps) {
  let iconSrc;

  // Priorité 1: Utiliser le provider s'il est spécifié
  if (provider) {
    const normalizedProvider = provider.toLowerCase().trim();
    iconSrc = providerIcons[normalizedProvider];
  }

  // Priorité 2: Essayer de déduire le provider à partir du modèle
  if (!iconSrc) {
    const normalizedModel = model.toLowerCase().trim();

    // Vérifier si le modèle contient un préfixe de fournisseur (ex: "openai/gpt-4")
    const parts = normalizedModel.split('/');
    if (parts.length > 1) {
      const providerFromModel = parts[0].toLowerCase().trim();
      iconSrc = providerIcons[providerFromModel];
    }
  }

  // Priorité 3: Chercher dans le mapping des modèles spécifiques
  if (!iconSrc) {
    const normalizedModel = model.toLowerCase().trim();
    iconSrc = modelIcons[normalizedModel];
  }

  // Si toujours pas trouvé, utiliser l'icône OpenAI blanche par défaut
  if (!iconSrc) {
    iconSrc = openaiWhiteLogo;
  }

  return (
    <img
      src={iconSrc}
      alt={`${model} icon`}
      width={size}
      height={size}
      className={`${className}`}
    />
  );
}
