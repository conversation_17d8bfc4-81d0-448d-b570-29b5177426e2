/**
 * Formate une date en format lisible
 * @param timestamp Timestamp en millisecondes
 * @returns Date formatée (ex: "Il y a 2 heures" ou "12 janvier 2023")
 */
export const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  // Si moins d'une minute
  if (diffSecs < 60) {
    return "À l'instant";
  }
  
  // Si moins d'une heure
  if (diffMins < 60) {
    return `Il y a ${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'}`;
  }
  
  // Si moins d'un jour
  if (diffHours < 24) {
    return `Il y a ${diffHours} ${diffHours === 1 ? 'heure' : 'heures'}`;
  }
  
  // Si moins d'une semaine
  if (diffDays < 7) {
    return `Il y a ${diffDays} ${diffDays === 1 ? 'jour' : 'jours'}`;
  }
  
  // Sinon, afficher la date complète
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  };
  
  return date.toLocaleDateString('fr-FR', options);
};

/**
 * Tronque un texte s'il dépasse une certaine longueur
 * @param text Texte à tronquer
 * @param maxLength Longueur maximale
 * @returns Texte tronqué avec "..." si nécessaire
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
};
