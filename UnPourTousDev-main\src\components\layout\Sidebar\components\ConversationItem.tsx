import React from "react";
import { Link } from "react-router-dom";
import { formatDate } from "../utils";
import { ModelIconDisplay } from "../../../../components/features/models";

interface ConversationItemProps {
  conversation: any;
  isSelected: boolean;
  onSelect: () => void;
  onOpenMenu: (e: React.MouseEvent) => void;
  isRenaming: boolean;
  newTitle: string;
  setNewTitle: (title: string) => void;
  handleRename: (e: React.FormEvent) => void;
  confirmDelete: boolean;
  handleDelete: () => void;
  setConfirmDelete: (confirm: boolean) => void;
  renameInputRef: React.RefObject<HTMLInputElement | null>;
  isCollapsed: boolean;
}

/**
 * Composant pour un élément de conversation dans la liste
 */
const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isSelected,
  onSelect,
  onOpenMenu,
  isRenaming,
  newTitle,
  setNewTitle,
  handleRename,
  confirmDelete,
  handleDelete,
  setConfirmDelete,
  renameInputRef,
  isCollapsed
}) => {
  // Formatage de la date de création
  const formattedDate = formatDate(conversation._creationTime);

  return (
    <Link
      to={`/c/${conversation._id}`}
      className={`group relative block ${
        isSelected
          ? "bg-gray-200 dark:bg-gray-700"
          : "hover:bg-gray-100 dark:hover:bg-gray-800"
      } rounded-lg transition-colors cursor-pointer mb-1`}
      onClick={(e) => {
        // Empêcher la navigation si on est en train de renommer ou de supprimer
        if (isRenaming || confirmDelete) {
          e.preventDefault();
        } else {
          onSelect();
        }
      }}
    >
      {isRenaming ? (
        // Formulaire de renommage
        <form onSubmit={handleRename} className="p-2">
          <input
            ref={renameInputRef}
            type="text"
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            className="w-full p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            autoFocus
            onBlur={handleRename}
          />
        </form>
      ) : confirmDelete ? (
        // Confirmation de suppression
        <div className="p-2 flex flex-col">
          <p className="text-sm text-red-600 dark:text-red-400 mb-2">
            Supprimer cette conversation ?
          </p>
          <div className="flex space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded"
            >
              Supprimer
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setConfirmDelete(false);
              }}
              className="px-2 py-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 text-xs rounded"
            >
              Annuler
            </button>
          </div>
        </div>
      ) : (
        // Affichage normal
        <div className={`flex items-center p-2 ${isCollapsed ? 'justify-center' : ''}`}>
          {/* Icône de conversation - Utiliser le composant partagé */}
          <ModelIconDisplay
            conversation={conversation}
            size={16}
            className={`${isCollapsed ? '' : 'mr-3'}`}
          />

          {/* Titre et date (masqués en mode rétracté) */}
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {conversation.title}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formattedDate}
              </p>
            </div>
          )}

          {/* Bouton de menu (visible au survol ou en mode rétracté) */}
          <button
            onClick={onOpenMenu}
            className={`${
              isCollapsed
                ? "opacity-100 ml-0"
                : "opacity-0 group-hover:opacity-100 ml-2"
            } text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-opacity`}
            title="Options"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
              />
            </svg>
          </button>
        </div>
      )}
    </Link>
  );
};

export default ConversationItem;


