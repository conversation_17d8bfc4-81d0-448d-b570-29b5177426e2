import React, { useState, useEffect } from 'react';

// Composant pour tester et modifier les couleurs du thème avec Tailwind
const ThemeColorTester: React.FC = () => {
  // État pour le mode sombre
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Vérifier si le mode sombre est activé au chargement
  useEffect(() => {
    const darkModeEnabled = document.documentElement.classList.contains('dark');
    setIsDarkMode(darkModeEnabled);
  }, []);

  // Fonction pour basculer le mode sombre
  const toggleDarkMode = () => {
    if (isDarkMode) {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('darkMode', 'false');
    } else {
      document.documentElement.classList.add('dark');
      localStorage.setItem('darkMode', 'true');
    }
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className="p-4 bg-surface-light dark:bg-surface-dark rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4 text-text-light dark:text-text-dark">Testeur de couleurs du thème Tailwind</h2>

      <div className="space-y-4">
        {/* Contrôle du mode sombre */}
        <div className="flex items-center justify-between">
          <span className="text-text-light dark:text-text-dark">Mode sombre</span>
          <button
            onClick={toggleDarkMode}
            className="px-4 py-2 bg-gray-200 dark:bg-border-dark rounded-md text-text-light dark:text-text-dark"
          >
            {isDarkMode ? 'Désactiver' : 'Activer'}
          </button>
        </div>

        {/* Information sur la configuration des couleurs */}
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <h3 className="text-md font-semibold mb-2 text-text-light dark:text-text-dark">Configuration des couleurs</h3>
          <p className="text-sm text-text-light dark:text-text-dark mb-2">
            Les couleurs sont maintenant définies dans <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">tailwind.config.js</code>
          </p>
          <p className="text-sm text-text-light dark:text-text-dark">
            Pour modifier les couleurs, éditez le fichier de configuration et redémarrez le serveur.
          </p>
        </div>
      </div>

      {/* Aperçu des composants avec les nouvelles couleurs */}
      <div className="mt-8 space-y-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-text-dark">Aperçu des composants</h3>

        {/* Couleurs de base */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-text-light dark:text-text-dark">Couleurs de base</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-surface-light dark:bg-surface-dark border border-border-light dark:border-border-dark rounded-lg">
              <h5 className="font-medium text-text-light dark:text-text-dark">Couleur de fond</h5>
              <p className="text-sm text-text-light dark:text-text-dark">bg-surface-light / dark:bg-surface-dark</p>
            </div>
            <div className="p-4 bg-white dark:bg-gray-800 border border-border-light dark:border-border-dark rounded-lg">
              <h5 className="font-medium text-text-light dark:text-text-dark">Couleur de bordure</h5>
              <p className="text-sm text-text-light dark:text-text-dark">border-border-light / dark:border-border-dark</p>
            </div>
          </div>
        </div>

        {/* Boutons */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-text-light dark:text-text-dark">Boutons</h4>
          <div className="flex flex-wrap gap-2">
            <button className="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors">
              Bouton primaire
            </button>
            <button className="px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-md transition-colors">
              Bouton secondaire
            </button>
            <button className="px-4 py-2 bg-primary-light hover:bg-primary text-white rounded-md transition-colors">
              Bouton primaire clair
            </button>
            <button className="px-4 py-2 bg-secondary-light hover:bg-secondary text-white rounded-md transition-colors">
              Bouton secondaire clair
            </button>
          </div>
        </div>

        {/* Cartes */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-text-light dark:text-text-dark">Cartes</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-surface-light dark:bg-surface-dark border border-border-light dark:border-border-dark rounded-lg">
              <h5 className="font-medium text-text-light dark:text-text-dark">Carte 1</h5>
              <p className="text-text-light dark:text-text-dark text-sm">Exemple de contenu</p>
            </div>
            <div className="p-4 bg-surface-light dark:bg-surface-dark border border-border-light dark:border-border-dark rounded-lg">
              <h5 className="font-medium text-text-light dark:text-text-dark">Carte 2</h5>
              <p className="text-text-light dark:text-text-dark text-sm">Exemple de contenu</p>
            </div>
          </div>
        </div>

        {/* Formulaires */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-text-light dark:text-text-dark">Formulaires</h4>
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <label className="block text-text-light dark:text-text-dark">Champ de texte</label>
              <input
                type="text"
                placeholder="Exemple de champ de texte"
                className="w-full p-2 border border-border-light dark:border-border-dark rounded-md bg-surface-light dark:bg-surface-dark text-text-light dark:text-text-dark focus:outline-none focus:border-fire-700 focus:border-2"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-text-light dark:text-text-dark">Zone de texte</label>
              <textarea
                placeholder="Exemple de zone de texte"
                className="w-full p-2 border border-border-light dark:border-border-dark rounded-md bg-surface-light dark:bg-surface-dark text-text-light dark:text-text-dark focus:outline-none focus:border-fire-700 focus:border-2"
                rows={3}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeColorTester;
