# TASK - Tâches pour UnPourTous (React/Tailwind/Convex)

Ces tâches représentent les étapes pour construire les fonctionnalités de l'application décrites dans `PLANNING.md`. Les tâches cochées ont été accomplies.

## Phase 0: Préparation de l'Environnement ✅

* [x] **T0.1:** Installer Node.js et npm/yarn.
* [x] **T0.2:** Installer Git (si pas déjà fait).
* [x] **T0.3:** Créer un compte Convex et installer la CLI Convex (`npm install -g convex`).
* [x] **T0.4:** Créer un compte OpenRouter et obtenir une clé API.
* [x] **T0.5:** Initialiser un nouveau projet Convex (`npx convex init`). Choisir le template React/Vite si possible.
* [x] **T0.6:** Configurer les variables d'environnement Convex (via le dashboard Convex ou le fichier `.env.local`) pour la clé API OpenRouter. **Ajouter `.env.*` au `.gitignore` !**

## Phase 1: Mise en Place du Backend (Convex) ✅

* [x] **T1.1: Définir Schéma DB Initial**
    * Dans `convex/schema.ts`, définir les tables :
        * `users` (avec champs pour l'authentification si Convex Auth est utilisé directement, ou référence à l'ID d'authentification externe).
        * `conversations` (ex: `userId`, `title` - optionnel initialement).
        * `messages` (ex: `conversationId`, `author` ('user'/'assistant'), `content`, `timestamp`).
* [x] **T1.2: Setup Authentification**
    * Configurer Convex Auth dans `convex/auth.config.ts` pour utiliser Clerk.
    * Configurer le template JWT dans Clerk pour l'authentification avec Convex.
* [x] **T1.3: Créer Fonctions/Mutations/Actions de Base (Chat)**
    * Créer une query `listConversations` pour récupérer les conversations de l'utilisateur authentifié.
    * Créer une query `listMessages(conversationId)` pour récupérer les messages d'une conversation.
    * Créer une mutation `createConversation` (peut-être appelée implicitement au premier message).
    * Créer une mutation `sendMessage(conversationId, messageContent)` pour ajouter le message utilisateur.
    * Créer une action `getAiResponse(conversationId, messageContent)` qui sera appelée par `sendMessage` pour interroger OpenRouter.
* [x] **T1.4: Implémenter Logique Appel OpenRouter (Simulation)**
    * Dans l'action `getAiResponse`, implémenter une simulation de réponse IA.
    * Préparer la structure pour l'intégration future avec l'API OpenRouter.
    * Gérer la réponse et sauvegarder le message de l'IA via une mutation interne.

## Phase 2: Développement Frontend (React & Tailwind) ✅

* [x] **T2.1: Setup Tailwind CSS**
    * Suivre les instructions d'installation de Tailwind CSS pour Vite/React.
    * Configurer `tailwind.config.js`.
* [x] **T2.2: Créer Composants UI de Base**
    * `SignInForm` et `SignOutButton` (pour login/signup, utilisant les hooks Clerk).
    * `ChatLayout` (structure principale avec Sidebar et Main Area).
    * `Sidebar` (affichage simple de la liste des conversations).
    * `ChatArea` (affichage des messages `UserMessage`, `AiMessage`).
    * `MessageInput` (barre de saisie avec bouton d'envoi).
    * `SettingsPage` (page de paramètres pour gérer les modèles et les préférences).
* [x] **T2.3: Implémenter Logique Authentification Frontend**
    * Utiliser les hooks Clerk et Convex pour gérer l'état d'authentification.
    * Protéger les routes/composants nécessitant une authentification.
    * Afficher le formulaire de connexion/inscription si non authentifié.
* [x] **T2.4: Implémenter Logique Chat Frontend**
    * Récupérer et afficher la liste des conversations dans la `Sidebar`.
    * Permettre la sélection d'une conversation.
    * Récupérer et afficher les messages de la conversation sélectionnée dans `ChatArea`.
    * Connecter `MessageInput` pour appeler la mutation `sendMessage`.

## Phase 3: Intégration et Tests Initiaux ✅

* [x] **T3.1: Tester Flux Authentification**
    * Tester l'inscription, la connexion et la déconnexion.
    * Vérifier que l'état d'authentification est correctement géré dans l'UI.
* [x] **T3.2: Tester Flux Chat E2E (End-to-End)**
    * Se connecter.
    * Envoyer un premier message (devrait créer une conversation si nécessaire).
    * Vérifier que le message utilisateur s'affiche.
    * Vérifier que la réponse de l'IA s'affiche dans le chat.
    * Envoyer d'autres messages dans la même conversation.
* [x] **T3.3: Ajouter Gestion Erreurs Simple (Frontend/Backend)**
    * Afficher des messages d'erreur basiques dans l'UI si les mutations/actions échouent.
    * Logguer les erreurs côté backend Convex.
* [x] **T3.4: Styling Initial et Raffinement UI**
    * Appliquer le styling Tailwind pour se rapprocher de l'apparence souhaitée.
    * Assurer une bonne lisibilité et une interaction de base fluide.

## Phase 4: Fonctionnalités Supplémentaires ✅

* [x] **T4.1: Sélection de Modèle IA**
    * Ajouter une table `models` dans le schéma Convex.
    * Créer des fonctions pour gérer les modèles (liste, activation/désactivation).
    * Implémenter un sélecteur de modèle dans l'interface.
* [x] **T4.2: Page de Paramètres**
    * Créer une page de paramètres pour gérer les modèles et les préférences utilisateur.
    * Ajouter des sections pour la gestion du profil et des préférences.
    * Implémenter un toggle pour le thème sombre/clair.
* [x] **T4.3: Fonction "Reprompt With"**
    * Ajouter une fonction pour réessayer avec un autre modèle.
    * Implémenter l'interface utilisateur pour cette fonctionnalité.

## Phase 5: Authentification et Expérience Utilisateur Améliorées ✅

* [x] **T5.1: Page de Connexion Dédiée**
    * Créer une page de connexion attrayante avec Clerk.
    * Implémenter la persistance de session entre les visites.
    * Configurer la redirection automatique pour les utilisateurs déjà connectés.
* [x] **T5.2: Intégration du Composant UserProfile de Clerk**
    * Intégrer le composant UserProfile de Clerk dans la page des paramètres.
    * Configurer l'apparence pour qu'elle s'intègre au design de l'application.
    * Permettre aux utilisateurs de gérer leur compte directement dans l'application.
* [x] **T5.3: Implémentation du Mode Sombre**
    * Créer un contexte de thème pour gérer l'état du thème dans toute l'application.
    * Configurer Tailwind CSS pour le mode sombre.
    * Ajouter un toggle dans la page des paramètres pour basculer entre les thèmes.
    * Adapter tous les composants pour qu'ils s'adaptent au thème actuel.

## Phase 6: Intégration avec OpenRouter et Améliorations UI

* [x] **T6.1: Intégration Réelle avec OpenRouter**
    * Remplacer la simulation par des appels réels à l'API OpenRouter.
    * Gérer les erreurs et les limites de l'API.
    * Implémenter une gestion sécurisée de la clé API.
    * Ajouter un mode de secours en cas d'erreur ou si la clé API n'est pas configurée.
* [x] **T6.2: Mise à jour des Modèles Disponibles**
    * Mettre à jour la liste des modèles pour inclure les modèles supportés par OpenRouter.
    * Ajouter une fonction pour mettre à jour les modèles existants.
    * Améliorer l'affichage des noms de modèles dans l'interface.
* [x] **T6.3: Indicateurs de Chargement**
    * Créer un composant d'indicateur de chargement pour les réponses de l'IA.
    * Détecter automatiquement quand l'IA est en train de générer une réponse.
    * Afficher le nom du modèle utilisé pendant le chargement.

## Phase 7: Amélioration de l'Interface de Gestion des Modèles

* [x] **T7.1: Optimisation de l'Affichage du Tableau**
    * Rendre le tableau entièrement visible sans défilement horizontal.
    * Optimiser l'espace disponible avec des largeurs de colonnes appropriées.
    * Améliorer la lisibilité des informations dans les cellules.
* [x] **T7.2: Indication des Modèles Gratuits**
    * Afficher "FREE" pour les modèles dont les prix sont à zéro.
    * Mettre en évidence visuellement les modèles gratuits.
* [x] **T7.3: Tri du Tableau**
    * Implémenter le tri du tableau en cliquant sur les en-têtes de colonnes.
    * Permettre le tri par nom, fournisseur, contexte, prix et statut.
    * Ajouter des indicateurs visuels pour la colonne et la direction de tri.
* [x] **T7.4: Activation/Désactivation Groupée**
    * Ajouter un bouton unique pour activer/désactiver tous les modèles filtrés.
    * Optimiser les performances avec une seule requête à la base de données.
    * Afficher un compteur des modèles activés.

## Phase 8: Prochaines Étapes (À faire)

* [ ] **T8.1: Correction des Problèmes d'Intégration OpenRouter**
    * Résoudre les erreurs lors de l'utilisation des modèles dans le chat.
    * Améliorer la gestion des erreurs et les messages d'erreur.
* [ ] **T8.2: Streaming des Réponses**
    * Implémenter le streaming pour afficher les réponses de l'IA au fur et à mesure.
    * Ajouter des indicateurs visuels pendant le streaming.
* [x] **T8.3: Gestion des Conversations**
    * Ajouter un bouton "+" dans la sidebar pour créer une nouvelle conversation.
    * Implémenter la création de conversations vides.
* [x] **T8.4: Gestion Avancée des Conversations**
    * Ajouter un menu contextuel "..." pour chaque conversation.
    * Implémenter la fonctionnalité de renommage des conversations.
    * Implémenter la suppression des conversations avec confirmation.
* [x] **T8.5: Organisation des Conversations en Dossiers**
    * Créer une structure de dossiers pour organiser les conversations.
    * Ajouter la possibilité de créer, renommer et supprimer des dossiers.
    * Implémenter la fonctionnalité de déplacement des conversations entre dossiers.
* [x] **T8.6: Streaming des Réponses**
    * Modifier le schéma de données pour ajouter un champ `isStreaming` aux messages.
    * Créer des mutations pour gérer le streaming des messages.
    * Simuler le streaming des réponses de l'IA mot par mot.
    * Mettre à jour l'interface utilisateur pour afficher le texte en streaming avec un curseur clignotant.
* [ ] **T8.7: Recherche dans les Conversations**
    * Ajouter une fonction de recherche dans les conversations.
* [x] **T8.8: Amélioration de l'Interface Utilisateur**
    * Implémenter des notifications toast pour les actions importantes.
    * Améliorer davantage l'accessibilité de l'application.
    * Implémenter une sidebar rétractable pour optimiser l'espace d'affichage.
    * Ajouter un bouton de bascule pour rétracter/développer la sidebar.
    * Remplacer le bouton paramètres par un logo en bas de la sidebar.
    * Ajouter un menu déroulant pour les paramètres et la déconnexion.
* [ ] **T8.9: Tests et Déploiement**
    * Ajouter des tests unitaires et d'intégration.
    * Préparer l'application pour le déploiement en production.
    * Documenter le processus de déploiement.
* [x] **T8.10: Amélioration du Thème et des Couleurs**
    * Centraliser les couleurs du thème dans tailwind.config.js.
    * Remplacer les valeurs hexadécimales codées en dur par des classes Tailwind sémantiques.
    * Créer une palette de couleurs cohérente basée sur les couleurs du logo.
    * Uniformiser les couleurs de sélection des modèles dans les différentes catégories.

## Phase 9: Interface d'Administration ✅

* [x] **T9.1: Configuration de l'Interface Admin**
    * Créer un dossier admin pour l'interface d'administration.
    * Configurer Vite, React et Tailwind CSS pour l'interface admin.
    * Configurer Clerk pour l'authentification de l'interface admin.
* [x] **T9.2: Authentification Admin avec Clerk**
    * Implémenter l'authentification avec Clerk pour l'interface admin.
    * Restreindre l'accès à l'interface admin aux utilisateurs ayant le rôle "admin" dans leurs métadonnées publiques.
    * Créer une page d'accès refusé pour les utilisateurs non autorisés.
* [x] **T9.3: Gestion des Modèles dans l'Interface Admin**
    * Connecter l'interface admin à la base de données Convex de l'application principale.
    * Implémenter l'affichage des modèles dans un tableau avec tri et filtrage.
    * Ajouter des fonctionnalités pour activer/désactiver les modèles.
    * Implémenter l'ajout de modèles personnalisés.
    * Ajouter la possibilité de modifier les modèles existants.
    * Implémenter la suppression des modèles personnalisés.
* [x] **T9.4: Synchronisation avec OpenRouter**
    * Implémenter la synchronisation des modèles avec OpenRouter.
    * Afficher les capacités des modèles (recherche web, sortie structurée, etc.).
    * Mettre à jour les modèles existants avec les informations les plus récentes.
    * Configurer les nouveaux modèles pour qu'ils soient désactivés par défaut lors de la synchronisation.
* [x] **T9.5: Catégorisation des Modèles**
    * Ajouter une nouvelle catégorie "Chat" pour les modèles.
    * Implémenter des cases à cocher pour modifier les catégories des modèles (Recherche Web/Raisonnement/Chat).
    * Afficher des badges visuels pour indiquer les catégories des modèles dans le tableau.
* [x] **T9.6: Synchronisation avec la Production**
    * Créer une table "livemodels" dans la base de données Convex pour les modèles en production.
    * Implémenter un bouton "ENVOYER LIVE" pour envoyer les modèles activés vers la table de production.
    * Assurer que seuls les modèles activés sont envoyés en production.