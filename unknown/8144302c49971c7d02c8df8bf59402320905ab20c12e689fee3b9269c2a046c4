import { useEffect, RefObject } from 'react';

/**
 * Hook personnalisé pour détecter les clics en dehors d'un élément
 * @param ref Référence à l'élément à surveiller
 * @param callback Fonction à appeler lorsqu'un clic est détecté en dehors de l'élément
 */
export function useClickOutside(
  ref: RefObject<HTMLElement | null>,
  callback: () => void
): void {
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback();
      }
    }

    // Ajoute l'écouteur d'événements au document
    document.addEventListener('mousedown', handleClickOutside);

    // Nettoie l'écouteur d'événements
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, callback]);
}
