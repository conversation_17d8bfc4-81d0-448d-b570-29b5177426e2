// Configuration des limites de tokens par catégorie de modèle
export const TOKEN_LIMITS = {
  // Modèles de chat standard
  CHAT: {
    DEFAULT: 2000,
    // Modèles spécifiques
    "google/gemini-2.5-pro-preview-03-25": 8000,
    "google/gemini-2.5-flash-preview:thinking": 4000,
    "openai/o4-mini": 4000,
    "openai/gpt-4.1": 4000,
    "x-ai/grok-3-beta": 4000,
    "meta-llama/llama-4-maverick": 4000,
    "anthropic/claude-3.7-sonnet": 4000,
    "mistralai/mistral-large-2411": 4000,
    "deepseek/deepseek-r1": 4000,
  },
  
  // Modèles avec capacité de recherche web
  WEB_SEARCH: {
    DEFAULT: 4000,
    // Modèles spécifiques
    "perplexity/sonar": 8000,
    "perplexity/sonar-small-online": 8000,
    "perplexity/sonar-medium-online": 8000,
  },
  
  // Modèles avec capacité de raisonnement avancé
  REASONING: {
    DEFAULT: 4000,
    // Modèles spécifiques
    "google/gemini-2.5-pro-preview-03-25": 8000,
    "anthropic/claude-3.7-sonnet": 6000,
    "openai/gpt-4.1": 6000,
  },
  
  // Valeur par défaut pour tous les modèles
  DEFAULT: 2000
};

// Configuration des modèles qui supportent le streaming
export const STREAMING_SUPPORTED_MODELS = [
  "google/gemini-2.5-pro-preview-03-25",
  "google/gemini-2.5-flash-preview:thinking",
  "openai/o4-mini",
  "openai/gpt-4.1",
  "anthropic/claude-3.7-sonnet",
  "perplexity/sonar",
  "perplexity/sonar-small-online",
  "perplexity/sonar-medium-online",
  "mistralai/mistral-large-2411",
  "meta-llama/llama-4-maverick",
  "x-ai/grok-3-beta",
  "deepseek/deepseek-r1",
  "openrouter/auto" // L'AutoRouter supporte le streaming si le modèle choisi le supporte
];
