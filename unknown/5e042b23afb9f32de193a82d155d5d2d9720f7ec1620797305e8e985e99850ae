import { SignIn, SignUp } from "@clerk/clerk-react";
import { useState, useEffect } from "react";
import { clerkConfig } from "../clerk-config";
import { useNavigate } from "react-router-dom";
import logo from "../assets/logo2.svg";
import { dark } from "@clerk/themes";
import { useTheme } from "../contexts/ThemeContext";

export default function LoginPage() {
  const [view, setView] = useState<"signIn" | "signUp">("signIn");
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();

  // Fonction pour gérer la redirection après une connexion réussie
  const handleRedirectCallback = () => {
    navigate("/");
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-surface-light to-gray-100 dark:from-surface-dark dark:to-gray-900 p-4 transition-colors duration-200">
      <div className="w-full max-w-md">
        {/* Logo et titre */}
        <div className="text-center mb-8">
          <img src={logo} alt="UnPourTous Logo" className="h-16 mx-auto mb-4" />
          <p className="text-text-light dark:text-text-dark mt-2">
            Un seul endroit pour tous vos modèles d'IA
          </p>
        </div>

        {/* Carte d'authentification */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-6">
          {/* Onglets */}
          <div className="flex mb-6 border-b dark:border-gray-700">
            <button
              className={`flex-1 py-3 font-medium text-sm ${
                view === "signIn"
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
              onClick={() => setView("signIn")}
            >
              Connexion
            </button>
            <button
              className={`flex-1 py-3 font-medium text-sm ${
                view === "signUp"
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
              onClick={() => setView("signUp")}
            >
              Inscription
            </button>
          </div>

          {/* Formulaires Clerk */}
          <div className="mt-4">
            {view === "signIn" ? (
              <SignIn
                appearance={{
                  ...clerkConfig.appearance,
                  baseTheme: isDarkMode ? dark : undefined,
                  variables: {
                    ...clerkConfig.appearance.variables,
                    colorBackground: isDarkMode ? "#1f2937" : "#ffffff",
                    colorText: isDarkMode ? "#f3f4f6" : "#1f2937",
                    colorTextSecondary: isDarkMode ? "#d1d5db" : "#4b5563",
                    colorInputBackground: isDarkMode ? "#374151" : "#f3f4f6",
                    colorInputText: isDarkMode ? "#f3f4f6" : "#1f2937",
                  }
                }}
                redirectUrl="/"
                signUpUrl="#"
                afterSignInUrl="/"
                routing="hash"
                afterSignInCallback={handleRedirectCallback}
              />
            ) : (
              <SignUp
                appearance={{
                  ...clerkConfig.appearance,
                  baseTheme: isDarkMode ? dark : undefined,
                  variables: {
                    ...clerkConfig.appearance.variables,
                    colorBackground: isDarkMode ? "#1f2937" : "#ffffff",
                    colorText: isDarkMode ? "#f3f4f6" : "#1f2937",
                    colorTextSecondary: isDarkMode ? "#d1d5db" : "#4b5563",
                    colorInputBackground: isDarkMode ? "#374151" : "#f3f4f6",
                    colorInputText: isDarkMode ? "#f3f4f6" : "#1f2937",
                  }
                }}
                redirectUrl="/"
                signInUrl="#"
                afterSignUpUrl="/"
                routing="hash"
                afterSignUpCallback={handleRedirectCallback}
              />
            )}
          </div>
        </div>

        {/* Pied de page */}
        <div className="text-center text-gray-500 dark:text-gray-400 text-sm">
          <p>
            En vous connectant, vous acceptez nos{" "}
            <a href="#" className="text-primary hover:underline">
              Conditions d'utilisation
            </a>{" "}
            et notre{" "}
            <a href="#" className="text-primary hover:underline">
              Politique de confidentialité
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
