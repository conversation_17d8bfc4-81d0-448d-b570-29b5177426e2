# PLANNING - UnPourTous (React/Tailwind/Convex)

## 1. Titre du Projet

UnPourTous - Interface de Chat Agrégateur IA

## 2. Introduction / Vision

Ce document décrit le plan de développement pour une application web full-stack servant d'interface de chat unifiée pour interagir avec différents modèles d'IA via l'API OpenRouter. L'objectif est de fournir une expérience utilisateur fluide, inspirée de l'interface de chat d'OpenAI, tout en s'appuyant sur une stack moderne avec React, Tailwind CSS et Convex pour le backend et la base de données.

## 3. État Actuel du Projet

L'application a atteint un stade fonctionnel avec les fonctionnalités suivantes implémentées :

* **Authentification Utilisateur :**
    * Implémentation de l'inscription et de la connexion en utilisant Clerk.
    * Intégration de Clerk avec Convex via JWT.
    * Page de connexion dédiée avec persistance de session.
    * Redirection automatique des utilisateurs déjà connectés.
    * Intégration du composant UserProfile de Clerk pour la gestion du compte.
* **Interface de Chat :**
    * Layout principal avec zone de chat centrale, sidebar gauche et barre de saisie.
    * Sidebar rétractable pour optimiser l'espace d'affichage.
    * Bouton de bascule pour rétracter/développer la sidebar.
    * Menu déroulant pour les paramètres et la déconnexion.
    * Distinction claire entre les messages de l'utilisateur et de l'IA.
    * Support du mode sombre/clair avec persistance de la préférence.
    * Indicateurs de chargement pendant la génération des réponses.
* **Intégration Backend :**
    * Utilisation de Convex pour la logique backend et la base de données temps réel.
    * Stockage des conversations, messages et modèles.
    * Intégration avec l'API OpenRouter pour les réponses IA.
* **Gestion des Modèles :**
    * Sélection de modèle IA dans l'interface.
    * Support pour une large gamme de modèles (GPT, Claude, Llama, Mistral).
    * Interface avancée de gestion des modèles avec tri, filtrage et actions groupées.
    * Synchronisation avec l'API OpenRouter pour obtenir les modèles disponibles.
    * Indication claire des modèles gratuits et personnalisés.
    * Fonction "Reprompt With" pour réessayer avec un autre modèle.
* **Interface d'Administration :**
    * Interface dédiée pour la gestion des modèles.
    * Authentification sécurisée avec Clerk et restriction d'accès basée sur les rôles.
    * Tableau de bord pour visualiser, ajouter, modifier et supprimer des modèles.
    * Fonctionnalités avancées de tri, filtrage et actions groupées.
    * Synchronisation avec OpenRouter pour obtenir les modèles disponibles.
    * Gestion des capacités des modèles (recherche web, raisonnement, chat, etc.).
    * Catégorisation des modèles avec badges visuels.
    * Désactivation par défaut des nouveaux modèles lors de la synchronisation.
    * Synchronisation des modèles activés vers une table de production via un bouton "ENVOYER LIVE".
* **Expérience Utilisateur :**
    * Thème sombre/clair basé sur les préférences système ou le choix de l'utilisateur.
    * Système de thème centralisé avec palette de couleurs cohérente basée sur les couleurs du logo.
    * Couleurs sémantiques définies dans tailwind.config.js pour une maintenance facilitée.
    * Interface responsive et accessible.
    * Transitions fluides entre les différents états de l'application.
    * Affichage du nom du modèle utilisé pour chaque message.
    * Infobulles détaillées pour les informations supplémentaires.
    * Expérience utilisateur cohérente avec des couleurs de sélection uniformes pour les modèles.

## 4. Fonctionnalités à Développer

**Prochaines Étapes :**

* **Correction des Problèmes d'Intégration OpenRouter :**
    * Résoudre les erreurs lors de l'utilisation des modèles dans le chat.
    * Améliorer la gestion des erreurs et les messages d'erreur.
    * Assurer une communication fiable avec l'API OpenRouter.
* **Streaming des Réponses :**
    * Implémenter le streaming pour afficher les réponses de l'IA au fur et à mesure.
    * Ajouter des indicateurs visuels pendant le streaming.
    * Optimiser l'expérience utilisateur pendant le streaming.
* **Gestion des Conversations :**
    * Création de nouvelles conversations via un bouton dédié.
    * Menu contextuel pour chaque conversation avec options de renommage et suppression.
    * Confirmation avant suppression pour éviter les erreurs.
    * Organisation des conversations en dossiers.
    * Création, renommage et suppression de dossiers.
    * Déplacement des conversations entre dossiers.
    * Interface intuitive pour la gestion des conversations.
* **Expérience Utilisateur Améliorée :**
    * Streaming des réponses de l'IA mot par mot pour une expérience plus naturelle.
    * Affichage d'un curseur clignotant pendant la génération des réponses.
    * Support du Markdown pour une meilleure mise en forme des réponses.
* **Gestion Avancée des Conversations :**
    * Rechercher dans les conversations.
* **Amélioration de l'Interface Utilisateur :**
    * Sidebar rétractable pour optimiser l'espace d'affichage.
    * Menu déroulant pour les paramètres et la déconnexion.
    * Notifications toast pour les actions importantes.
    * Amélioration continue de l'accessibilité.
    * Optimisation pour les appareils mobiles.

**Fonctionnalités Futures (Long Terme) :**

* Fonctionnalités RAG (Retrieval-Augmented Generation).
* Personnalisation avancée des modèles (paramètres de génération).
* Partage de conversations.
* Exportation des conversations en différents formats.
* Intégration avec d'autres fournisseurs d'IA.

## 5. Technologies Utilisées

* **Frontend :**
    * React 19 avec TypeScript
    * Tailwind CSS pour le styling
    * Radix UI pour les composants
    * React Router pour la navigation
* **Backend & Base de Données :**
    * Convex pour les fonctions cloud et la base de données temps réel
* **Authentification :**
    * Clerk pour la gestion des utilisateurs et l'authentification
    * Gestion des rôles utilisateurs via les métadonnées publiques de Clerk
* **API Modèles IA :**
    * OpenRouter pour l'accès à une variété de modèles d'IA
* **Interface d'Administration :**
    * Application React séparée dans le dossier admin
    * Partage de la même base de données Convex que l'application principale
    * Authentification sécurisée avec restriction basée sur les rôles

## 6. Architecture Actuelle

### Structure de la Base de Données

* **conversations :**
    * `userId` : ID de l'utilisateur propriétaire (provenant de Clerk)
    * `title` : Titre de la conversation
    * Index sur `userId` pour récupérer rapidement les conversations d'un utilisateur

* **messages :**
    * `conversationId` : Référence à la conversation
    * `role` : "user" ou "assistant"
    * `content` : Contenu du message
    * `modelUsed` : Modèle d'IA utilisé
    * Index sur `conversationId` pour récupérer les messages d'une conversation

* **models :**
    * `name` : Nom du modèle
    * `modelId` : ID du modèle
    * `provider` : Fournisseur du modèle
    * `description` : Description optionnelle
    * `enabled` : Si le modèle est activé
    * Index sur `name` pour rechercher rapidement un modèle par son nom

### Flux Principal (Chat)

1. L'utilisateur authentifié tape un message dans la barre de saisie et l'envoie.
2. Le composant React capture le message et appelle la mutation `sendMessage`.
3. La mutation `sendMessage` côté backend Convex :
   a. Valide l'authentification de l'utilisateur via Clerk.
   b. Sauvegarde le message utilisateur dans la base de données Convex.
   c. Appelle l'action `getAiResponse` pour obtenir une réponse.
4. L'action `getAiResponse` simule actuellement une réponse IA.
5. L'action sauvegarde le message de l'IA dans la base de données Convex.
6. Le frontend React, abonné aux queries Convex, se met à jour automatiquement et affiche le nouveau message de l'IA.

## 7. Considérations Clés / Risques

* **Intégration Clerk-Convex :** Assurer une configuration correcte du template JWT dans Clerk et de l'authentification dans Convex.
* **Gestion Clé API OpenRouter :** Stocker de manière sécurisée dans les variables d'environnement Convex, jamais dans le code frontend.
* **Coûts OpenRouter :** Surveiller l'utilisation de l'API pour éviter les coûts imprévus.
* **Performance :** Optimiser les requêtes Convex pour maintenir de bonnes performances même avec un grand nombre de conversations et messages.
* **Sécurité :** S'assurer que les utilisateurs ne peuvent accéder qu'à leurs propres conversations et messages.
* **Gestion des Rôles :** Maintenir une séparation claire entre les utilisateurs normaux et les administrateurs via les métadonnées Clerk.
* **Synchronisation des Données :** Assurer que les modifications apportées via l'interface d'administration sont immédiatement reflétées dans l'application principale.
* **Gestion des Modèles :** Désactiver par défaut les nouveaux modèles lors de la synchronisation avec OpenRouter pour éviter d'exposer accidentellement des modèles non testés aux utilisateurs.

## 8. Plan de Déploiement

1. **Finaliser l'Intégration OpenRouter :** Remplacer la simulation par des appels réels à l'API.
2. **Tests Approfondis :** Tester toutes les fonctionnalités dans différents scénarios.
3. **Optimisation :** Améliorer les performances et l'expérience utilisateur.
4. **Déploiement :** Déployer l'application sur une plateforme comme Vercel ou Netlify pour le frontend, et utiliser Convex Cloud pour le backend.
5. **Monitoring :** Mettre en place des outils de surveillance pour détecter les problèmes rapidement.

## 9. Conclusion

L'application UnPourTous a atteint un stade fonctionnel avec les fonctionnalités de base implémentées. L'ajout récent de l'interface d'administration permet une gestion plus efficace des modèles d'IA disponibles dans l'application. Les prochaines étapes se concentreront sur l'intégration réelle avec OpenRouter, l'amélioration de l'interface utilisateur et l'ajout de fonctionnalités avancées pour offrir une expérience utilisateur complète et fluide.

L'architecture actuelle, avec une application principale pour les utilisateurs et une interface d'administration séparée mais partageant la même base de données, offre une bonne séparation des préoccupations tout en maintenant la cohérence des données. La gestion des rôles via Clerk assure que seuls les administrateurs autorisés peuvent accéder à l'interface d'administration.

La catégorisation des modèles (Recherche Web, Raisonnement, Chat) et la désactivation par défaut des nouveaux modèles lors de la synchronisation avec OpenRouter permettent une gestion plus fine et sécurisée des modèles disponibles pour les utilisateurs. Les administrateurs peuvent ainsi tester les nouveaux modèles avant de les rendre disponibles aux utilisateurs.

L'ajout de la table "livemodels" et du bouton "ENVOYER LIVE" permet une séparation claire entre l'environnement de gestion des modèles et l'environnement de production. Cette approche offre un contrôle supplémentaire sur les modèles qui sont effectivement disponibles pour les utilisateurs finaux, tout en permettant aux administrateurs de tester et configurer les modèles avant de les mettre en production.