import { action } from "../_generated/server";
import { v } from "convex/values";

// Types pour l'API OpenRouter
export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
    image: string;
    request: string;
  };
  architecture?: {
    input_modalities?: string[];
    output_modalities?: string[];
    tokenizer?: string;
  };
  top_provider?: {
    is_moderated?: boolean;
  };
}

// Fonction pour récupérer les modèles disponibles via l'API OpenRouter
export const fetchAvailableModels = action({
  args: {},
  handler: async (ctx) => {
    try {
      // Récupère la clé API depuis les variables d'environnement
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API OpenRouter n'est pas configurée");
      }

      // Appelle l'API OpenRouter pour récupérer les modèles disponibles
      const response = await fetch("https://openrouter.ai/api/v1/models", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // Vérifie si la requête a réussi
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Erreur OpenRouter:", errorData);
        throw new Error(`Erreur OpenRouter: ${response.status} ${response.statusText}`);
      }

      // Parse la réponse
      const data = await response.json();

      // Retourne les modèles
      return data.data as OpenRouterModel[];
    } catch (error) {
      console.error("Erreur lors de la récupération des modèles OpenRouter:", error);
      throw error;
    }
  },
});
