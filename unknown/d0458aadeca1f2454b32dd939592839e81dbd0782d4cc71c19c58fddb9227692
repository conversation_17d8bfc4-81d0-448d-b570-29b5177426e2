import { query } from "./_generated/server";

// Cette fonction est utilisée pour obtenir l'ID de l'utilisateur authentifié
export const getAuthUserId = async (ctx: any) => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    return null;
  }
  return identity.subject;
};

// Fonction pour vérifier si l'utilisateur est authentifié
export const isAuthenticated = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    return !!identity;
  },
});
