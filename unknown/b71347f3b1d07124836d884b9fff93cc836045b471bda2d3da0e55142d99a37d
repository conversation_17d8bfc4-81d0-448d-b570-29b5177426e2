import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Liste tous les dossiers de l'utilisateur
export const list = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    return await ctx.db
      .query("folders")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("asc")
      .collect();
  },
});

// Crée un nouveau dossier
export const create = mutation({
  args: {
    name: v.string(),
    color: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Trouve l'ordre le plus élevé pour placer le nouveau dossier à la fin
    const folders = await ctx.db
      .query("folders")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .collect();

    const maxOrder = folders.reduce((max, folder) => {
      return folder.order !== undefined && folder.order > max ? folder.order : max;
    }, 0);

    // Crée le nouveau dossier
    return await ctx.db.insert("folders", {
      userId: identity.subject,
      name: args.name,
      color: args.color,
      order: maxOrder + 1,
    });
  },
});

// Renomme un dossier
export const rename = mutation({
  args: {
    id: v.id("folders"),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire du dossier
    const folder = await ctx.db.get(args.id);
    if (!folder) throw new Error("Dossier non trouvé");
    if (folder.userId !== identity.subject) throw new Error("Non autorisé");

    // Renomme le dossier
    await ctx.db.patch(args.id, { name: args.name });
    return { success: true };
  },
});

// Change la couleur d'un dossier
export const changeColor = mutation({
  args: {
    id: v.id("folders"),
    color: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire du dossier
    const folder = await ctx.db.get(args.id);
    if (!folder) throw new Error("Dossier non trouvé");
    if (folder.userId !== identity.subject) throw new Error("Non autorisé");

    // Change la couleur du dossier
    await ctx.db.patch(args.id, { color: args.color });
    return { success: true };
  },
});

// Supprime un dossier
export const remove = mutation({
  args: {
    id: v.id("folders"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire du dossier
    const folder = await ctx.db.get(args.id);
    if (!folder) throw new Error("Dossier non trouvé");
    if (folder.userId !== identity.subject) throw new Error("Non autorisé");

    // Récupère toutes les conversations dans ce dossier
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_folder", (q) => q.eq("folderId", args.id))
      .collect();

    // Déplace les conversations vers la racine (sans dossier)
    for (const conversation of conversations) {
      await ctx.db.patch(conversation._id, { folderId: undefined });
    }

    // Supprime le dossier
    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Réorganise les dossiers
export const reorder = mutation({
  args: {
    orderedIds: v.array(v.id("folders")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de tous les dossiers
    for (let i = 0; i < args.orderedIds.length; i++) {
      const folder = await ctx.db.get(args.orderedIds[i]);
      if (!folder) throw new Error(`Dossier ${args.orderedIds[i]} non trouvé`);
      if (folder.userId !== identity.subject) throw new Error("Non autorisé");

      // Met à jour l'ordre
      await ctx.db.patch(args.orderedIds[i], { order: i });
    }

    return { success: true };
  },
});

// Déplace un dossier dans un autre dossier
export const moveToFolder = mutation({
  args: {
    folderId: v.id("folders"),
    parentFolderId: v.optional(v.id("folders")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire du dossier
    const folder = await ctx.db.get(args.folderId);
    if (!folder) throw new Error("Dossier non trouvé");
    if (folder.userId !== identity.subject) throw new Error("Non autorisé");

    // Si un parentFolderId est fourni, vérifie que l'utilisateur est propriétaire du dossier parent
    if (args.parentFolderId) {
      const parentFolder = await ctx.db.get(args.parentFolderId);
      if (!parentFolder) throw new Error("Dossier parent non trouvé");
      if (parentFolder.userId !== identity.subject) throw new Error("Non autorisé");

      // Vérifie qu'on ne crée pas une boucle (un dossier ne peut pas être son propre parent)
      if (args.folderId === args.parentFolderId) {
        throw new Error("Un dossier ne peut pas être son propre parent");
      }
    }

    // Met à jour le dossier avec son nouveau parent
    await ctx.db.patch(args.folderId, {
      parentFolderId: args.parentFolderId
    });

    return { success: true };
  },
});
