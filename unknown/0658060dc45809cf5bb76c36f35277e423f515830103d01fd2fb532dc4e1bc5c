import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// <PERSON><PERSON><PERSON> ou met à jour un utilisateur
export const createOrUpdate = mutation({
  args: {
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    image: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Recherche l'utilisateur par email
    const existingUser = args.email
      ? await ctx.db
          .query("users")
          .withIndex("email", (q) => q.eq("email", args.email))
          .unique()
      : null;

    if (existingUser) {
      // Met à jour l'utilisateur existant
      return await ctx.db.patch(existingUser._id, {
        name: args.name,
        image: args.image,
      });
    }

    // Crée un nouvel utilisateur
    return await ctx.db.insert("users", {
      name: args.name,
      email: args.email,
      image: args.image,
    });
  },
});

// Récupère l'utilisateur courant
export const getCurrent = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const user = await ctx.db
      .query("users")
      .withIndex("email", (q) => q.eq("email", identity.subject))
      .unique();

    return user;
  },
});

// Alias pour getCurrent (pour compatibilité avec le composant SettingsPage)
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    // Si l'utilisateur est authentifié mais n'existe pas encore dans la base de données,
    // retourner un objet utilisateur basique avec les informations de Clerk
    if (identity) {
      const user = await ctx.db
        .query("users")
        .withIndex("email", (q) => q.eq("email", identity.subject))
        .unique();

      if (user) {
        return user;
      } else {
        // Retourner un objet utilisateur basique avec les informations de Clerk
        return {
          name: identity.name,
          email: identity.email,
          image: identity.pictureUrl,
        };
      }
    }

    return null;
  },
});
