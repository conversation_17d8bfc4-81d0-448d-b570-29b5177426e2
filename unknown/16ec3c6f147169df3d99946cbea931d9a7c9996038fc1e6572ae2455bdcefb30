// Configuration de Clerk pour l'application
export const clerkConfig = {
  // Durée de la session en secondes (30 jours par défaut)
  sessionDurationInSeconds: 60 * 60 * 24 * 30,
  
  // Redirection après connexion
  afterSignInUrl: '/',
  
  // Redirection après inscription
  afterSignUpUrl: '/',
  
  // Redirection après déconnexion
  afterSignOutUrl: '/login',
  
  // Apparence
  appearance: {
    layout: {
      socialButtonsVariant: 'iconButton',
      socialButtonsPlacement: 'bottom',
    },
    variables: {
      colorPrimary: '#3b82f6', // Bleu Tailwind
    },
    elements: {
      formButtonPrimary: 'bg-blue-500 hover:bg-blue-600 text-white',
      card: 'rounded-lg shadow-md',
    },
  },
};
