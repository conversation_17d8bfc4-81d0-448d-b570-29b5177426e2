# UnPourTous - Application de Chat

Une application de chat élégante et minimaliste construite avec React, Vite, Convex et Clerk.

## À propos

UnPourTous est une plateforme de chat qui permet aux utilisateurs de communiquer avec différents modèles d'intelligence artificielle.


Ce projet comprend deux applications :
1. L'application principale de chat (dans le répertoire racine)
2. L'application d'administration pour la gestion des modèles IA (dans le répertoire `admin`)

## Fonctionnalités

- Interface utilisateur minimaliste et élégante
- Authentification utilisateur via Clerk
- Backend en temps réel avec Convex
- Intégration avec OpenRouter pour les modèles d'IA
- Organisation des conversations en dossiers
- Thème sombre/clair personnalisé avec palette de couleurs cohérente
- Sélection de modèles par catégorie (Chat, Recherche Web, Raisonnement)

## Structure du projet

Le code frontend se trouve dans le répertoire `src` et est construit avec [Vite](https://vitejs.dev/).

Le code backend se trouve dans le répertoire `convex`.

## Système de thème

L'application utilise un système de thème centralisé basé sur Tailwind CSS. Les couleurs du thème sont définies dans le fichier `tailwind.config.js` et sont utilisées de manière cohérente dans toute l'application.

### Palette de couleurs

La palette de couleurs est basée sur les couleurs du logo UnPourTous :
- **Bronze** : #4b321c (metallic-bronze) - Utilisé pour les éléments principaux
- **Tango** : #e37314 - Utilisé pour les accents et les boutons
- **Fire** : #dc4b04 - Utilisé pour les actions importantes

### Utilisation des couleurs

Les couleurs sont organisées de manière sémantique dans le fichier `tailwind.config.js` :
- `surface` : Couleurs de fond (light/dark)
- `text` : Couleurs de texte (light/dark)
- `primary` : Couleurs d'accent primaire (DEFAULT/hover/light/dark)
- `secondary` : Couleurs d'accent secondaire (DEFAULT/hover/light/dark)
- `border` : Couleurs de bordure (light/dark)

### Mode sombre

Le mode sombre utilise une couleur de fond #0f0e0a avec des textes clairs pour un contraste optimal. L'application bascule automatiquement entre les modes clair et sombre en fonction des préférences de l'utilisateur.

## Développement local

### Application principale

```bash
# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

Cela lancera à la fois le backend Convex et le frontend React.

### Application d'administration

```bash
# Installer les dépendances
cd admin
npm install

# Démarrer le serveur de développement
npm run dev
```

L'application d'administration sera accessible à l'adresse http://localhost:5174.

### Lancer les deux applications simultanément

Un script batch est fourni pour lancer à la fois l'application principale et l'application d'administration :

```bash
# À la racine du projet
run-all.bat
```

Cela lancera :
- Le backend Convex sur le port par défaut
- L'application principale sur http://localhost:5173
- L'application d'administration sur http://localhost:5174

## Déploiement sur Render

### Configuration

1. Créez un nouveau service Web sur Render
2. Connectez votre dépôt GitHub "UnPourTousV0"
3. Configurez les paramètres suivants :
   - **Build Command** : `npm install && npm run build`
   - **Start Command** : `npm run start`

### Variables d'environnement requises

Ajoutez ces variables d'environnement dans votre configuration Render. Notez la distinction importante entre les variables exposées au frontend et celles qui sont sécurisées :

#### Variables exposées au frontend (accessibles dans le navigateur)
Ces variables sont préfixées par `VITE_` et sont injectées dans le code client :
```
VITE_CONVEX_URL=https://your-deployment-id.convex.cloud
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
```

#### Variables backend sécurisées (non exposées au frontend)
Ces variables ne sont pas préfixées par `VITE_` et ne sont accessibles que dans les fonctions Convex côté serveur :
```
CONVEX_DEPLOYMENT=your_convex_deployment_url
CLERK_SECRET_KEY=your_clerk_secret_key
OPENROUTER_API_KEY=your_openrouter_api_key
CLERK_JWT_ISSUER_URL=https://your-clerk-instance.clerk.accounts.dev
CLERK_JWT_KEY=your_clerk_jwt_key
```

> **Sécurité importante** : Les clés secrètes (comme CLERK_SECRET_KEY, OPENROUTER_API_KEY) ne sont jamais exposées au frontend. Elles sont uniquement utilisées dans les fonctions Convex côté serveur.
* If you're new to Convex, the [Overview](https://docs.convex.dev/understanding/) is a good place to start
* Check out the [Hosting and Deployment](https://docs.convex.dev/production/) docs for how to deploy your app
* Read the [Best Practices](https://docs.convex.dev/understanding/best-practices/) guide for tips on how to improve you app further
