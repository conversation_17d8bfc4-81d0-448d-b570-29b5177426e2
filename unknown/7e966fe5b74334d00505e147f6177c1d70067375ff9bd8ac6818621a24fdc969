import { internalMutation } from "../_generated/server";
import { v } from "convex/values";

// Sauvegarde un message de l'assistant
export const saveAssistantMessage = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    content: v.string(),
    modelId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: "assistant",
      content: args.content,
      modelUsed: args.modelId,
    });
  },
});

// Crée un message de l'assistant en mode streaming
export const startStreamingMessage = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
  },
  handler: async (ctx, args) => {
    // Crée un message vide avec isStreaming=true
    return await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: "assistant",
      content: "",
      modelUsed: args.modelId,
      isStreaming: true,
    });
  },
});

// Met à jour un message en streaming avec du nouveau contenu
export const appendToStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
    contentToAppend: v.string(),
  },
  handler: async (ctx, args) => {
    // Récupère le message actuel
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message non trouvé");

    // Vérifie que le message est en streaming
    if (!message.isStreaming) throw new Error("Ce message n'est pas en mode streaming");

    // Ajoute le nouveau contenu
    await ctx.db.patch(args.messageId, {
      content: message.content + args.contentToAppend,
    });
  },
});

// Finalise un message en streaming
export const finishStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    // Marque le message comme n'étant plus en streaming
    await ctx.db.patch(args.messageId, {
      isStreaming: false,
    });
  },
});
