import { action } from "./_generated/server";
import { v } from "convex/values";

// Liste des modèles supportés par NotDiamond
const NOTDIAMOND_SUPPORTED_MODELS = [
    "openai/gpt-4.5-preview-2025-02-27",
    "openai/gpt-4o-2024-08-06",
    "openai/gpt-4o-2024-05-13",
    "openai/gpt-4-turbo-2024-04-09",
    "openai/gpt-4-0125-preview",
    "openai/gpt-4-1106-preview",
    "openai/gpt-4-0613",
    "openai/gpt-3.5-turbo-0125",
    "openai/gpt-4o-mini-2024-07-18",
    "openai/chatgpt-4o-latest",
    "openai/o1-preview-2024-09-12",
    "openai/o1-mini-2024-09-12",
    "anthropic/claude-3-7-sonnet-20250219",
    "anthropic/claude-3-5-sonnet-latest",
    "anthropic/claude-3-5-sonnet-20241022",
    "anthropic/claude-3-5-haiku-20241022",
    "anthropic/claude-3-5-sonnet-20240620",
    "anthropic/claude-3-opus-20240229",
    "anthropic/claude-3-sonnet-20240229",
    "anthropic/claude-3-haiku-20240307",
    "anthropic/claude-2.1",
    "google/gemini-2.0-flash-001",
    "google/gemini-1.5-pro-latest",
    "google/gemini-1.5-flash-latest",
    "mistral/open-mixtral-8x22b",
    "mistral/codestral-latest",
    "mistral/open-mixtral-8x7b",
    "mistral/mistral-large-2407",
    "mistral/mistral-large-2402",
    "mistral/mistral-medium-latest",
    "mistral/mistral-small-latest",
    "mistral/open-mistral-7b",
    "mistral/open-mistral-nemo",
    "replicate/meta-llama-3-70b-instruct",
    "replicate/meta-llama-3-8b-instruct",
    "replicate/mixtral-8x7b-instruct-v0.1",
    "replicate/mistral-7b-instruct-v0.2",
    "replicate/meta-llama-3.1-405b-instruct",
    "togetherai/Llama-3-70b-chat-hf",
    "togetherai/Llama-3-8b-chat-hf",
    "togetherai/Meta-Llama-3.1-8B-Instruct-Turbo",
    "togetherai/Meta-Llama-3.1-70B-Instruct-Turbo",
    "togetherai/Meta-Llama-3.1-405B-Instruct-Turbo",
    "togetherai/Qwen2-72B-Instruct",
    "togetherai/Mixtral-8x22B-Instruct-v0.1",
    "togetherai/Mixtral-8x7B-Instruct-v0.1",
    "togetherai/Mistral-7B-Instruct-v0.2",
    "togetherai/DeepSeek-R1",
    "perplexity/sonar",
    "cohere/command-r-plus",
    "cohere/command-r"
];

// Interface pour les modèles disponibles
export interface ModelOption {
  modelId: string;
  name: string;
  provider: string;
  webSearch?: boolean;
  reasoning?: boolean;
}

// Fonction pour obtenir le meilleur modèle via NotDiamond.AI
export const getBestModel = action({
  args: {
    message: v.string(),
    availableModels: v.array(
      v.object({
        modelId: v.string(),
        name: v.string(),
        provider: v.string(),
        webSearch: v.optional(v.boolean()),
        reasoning: v.optional(v.boolean()),
      })
    ),
  },
  handler: async (ctx, args): Promise<{ modelId: string; modelName: string; success: boolean }> => {
    try {
      // Récupère la clé API depuis les variables d'environnement
      const apiKey = process.env.NOTDIAMOND_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API NotDiamond n'est pas configurée");
      }

      // Fonction pour mapper les modèles OpenRouter vers les modèles NotDiamond
      const mapToNotDiamondModel = (provider: string, modelName: string) => {
        // Ajustement pour mistralai -> mistral (NotDiamond utilise "mistral" au lieu de "mistralai")
        let notDiamondProvider = provider;
        if (provider === "mistralai") {
          notDiamondProvider = "mistral";
        }

        // Mappings spécifiques pour les modèles Google
        if (provider === "google") {
          // Gemini 2.5 Pro Preview -> gemini-1.5-pro-latest (version supportée par NotDiamond)
          if (modelName.includes("gemini-2.5-pro")) {
            return { provider: "google", model: "gemini-1.5-pro-latest" };
          }
          // Gemini 2.5 Flash Preview -> gemini-1.5-flash-latest (version supportée par NotDiamond)
          if (modelName.includes("gemini-2.5-flash")) {
            return { provider: "google", model: "gemini-1.5-flash-latest" };
          }
        }

        // Mappings spécifiques pour les modèles OpenAI
        if (provider === "openai") {
          // GPT-4.1 -> gpt-4-turbo-2024-04-09 (version supportée par NotDiamond)
          if (modelName === "gpt-4.1") {
            return { provider: "openai", model: "gpt-4-turbo-2024-04-09" };
          }
          // o4-mini -> gpt-4o-mini-2024-07-18 (version supportée par NotDiamond)
          if (modelName === "o4-mini") {
            return { provider: "openai", model: "gpt-4o-mini-2024-07-18" };
          }
        }

        // Gestion des suffixes comme ":thinking"
        if (modelName.includes(":")) {
          modelName = modelName.split(":")[0];
        }

        // Format standard: provider/model
        return { provider: notDiamondProvider, model: modelName };
      };

      // Filtrer les modèles disponibles pour ne garder que ceux supportés par NotDiamond
      const supportedModels = args.availableModels.filter(model => {
        const modelIdParts = model.modelId.split('/');
        const provider = model.provider || modelIdParts[0];
        const modelName = modelIdParts.length > 1 ? modelIdParts[1] : modelIdParts[0];
        
        // Mapper le modèle au format NotDiamond
        const mappedModel = mapToNotDiamondModel(provider, modelName);
        const fullModelId = `${mappedModel.provider}/${mappedModel.model}`;
        
        return NOTDIAMOND_SUPPORTED_MODELS.includes(fullModelId);
      });

      console.log("Modèles supportés par NotDiamond:", supportedModels.map(m => m.modelId));

      if (supportedModels.length === 0) {
        console.log("Aucun modèle supporté par NotDiamond n'est disponible");
        throw new Error("Aucun modèle supporté par NotDiamond n'est disponible");
      }

      // Prépare les modèles pour NotDiamond
      const notDiamondModels = supportedModels.map(model => {
        const modelIdParts = model.modelId.split('/');
        const provider = model.provider || modelIdParts[0];
        const modelName = modelIdParts.length > 1 ? modelIdParts[1] : modelIdParts[0];
        
        const mappedModel = mapToNotDiamondModel(provider, modelName);
        return {
          provider: mappedModel.provider,
          model: mappedModel.model
        };
      });

      console.log("Appel à NotDiamond avec les modèles:", notDiamondModels);

      // Appelle l'API NotDiamond pour déterminer le meilleur modèle
      const response = await fetch("https://api.notdiamond.ai/v2/modelRouter/modelSelect", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          messages: [{ role: "user", content: args.message }],
          llm_providers: notDiamondModels
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erreur NotDiamond:", errorText);
        throw new Error(`Erreur NotDiamond: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Réponse NotDiamond:", data);

      // Récupère le modèle recommandé
      if (!data.providers || data.providers.length === 0) {
        throw new Error("NotDiamond n'a pas recommandé de modèle");
      }
      
      const recommendedProvider = data.providers[0].provider;
      const recommendedModelName = data.providers[0].model;
      
      if (!recommendedProvider || !recommendedModelName) {
        throw new Error("NotDiamond a renvoyé une recommandation incomplète");
      }
      
      console.log(`NotDiamond recommande: ${recommendedProvider}/${recommendedModelName}`);
      
      // Trouver le modèle original correspondant
      const recommendedModel = args.availableModels.find(m => {
        const modelIdParts = m.modelId.split('/');
        const provider = m.provider || modelIdParts[0];
        const modelName = modelIdParts.length > 1 ? modelIdParts[1] : modelIdParts[0];
        
        // Vérifier si le provider correspond (en tenant compte du mapping mistral/mistralai)
        const providerMatches = 
          provider === recommendedProvider || 
          (provider === "mistralai" && recommendedProvider === "mistral");
        
        // Vérifier si le nom du modèle correspond
        // Pour les modèles spéciaux comme GPT-4.1 -> gpt-4-turbo-2024-04-09
        if (provider === "openai" && modelName === "gpt-4.1" && recommendedModelName === "gpt-4-turbo-2024-04-09") {
          return true;
        }
        
        // Pour les modèles spéciaux comme o4-mini -> gpt-4o-mini-2024-07-18
        if (provider === "openai" && modelName === "o4-mini" && recommendedModelName === "gpt-4o-mini-2024-07-18") {
          return true;
        }
        
        // Pour les modèles Gemini
        if (provider === "google" && modelName.includes("gemini-2.5-pro") && recommendedModelName === "gemini-1.5-pro-latest") {
          return true;
        }
        
        if (provider === "google" && modelName.includes("gemini-2.5-flash") && recommendedModelName === "gemini-1.5-flash-latest") {
          return true;
        }
        
        return providerMatches && (modelName === recommendedModelName || m.modelId.endsWith(recommendedModelName));
      });

      if (!recommendedModel) {
        console.log(`Modèle recommandé par NotDiamond (${recommendedProvider}/${recommendedModelName}) non trouvé dans les modèles disponibles`);
        
        // Essayer de trouver un modèle similaire
        const fallbackModel = args.availableModels.find(m => 
          m.modelId.includes(recommendedProvider) || 
          (recommendedProvider === "mistral" && m.modelId.includes("mistralai"))
        );
        
        if (fallbackModel) {
          console.log(`Utilisation du modèle similaire: ${fallbackModel.modelId}`);
          return {
            modelId: fallbackModel.modelId,
            modelName: fallbackModel.name,
            success: true
          };
        }
        
        throw new Error(`Modèle recommandé par NotDiamond (${recommendedProvider}/${recommendedModelName}) non trouvé`);
      }

      console.log(`NotDiamond recommande le modèle: ${recommendedModel.modelId} (${recommendedModel.name})`);

      return {
        modelId: recommendedModel.modelId,
        modelName: recommendedModel.name,
        success: true
      };
    } catch (error) {
      console.error("Erreur lors de l'appel à NotDiamond:", error);
      // En cas d'erreur, retourne null pour indiquer qu'il faut utiliser le fallback
      throw error;
    }
  },
});




