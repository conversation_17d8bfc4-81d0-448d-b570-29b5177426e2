import React from "react";
import { AutoRouterButtonProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant pour le bouton AutoRouter
 */
const AutoRouterButton: React.FC<AutoRouterButtonProps> = ({ isSelected, onClick }) => {
  return (
    <div className="mt-6 mb-4">
      <button
        type="button"
        onClick={(e) => {
          e.preventDefault(); // Empêcher la soumission du formulaire
          onClick();
        }}
        className={`flex items-center p-3 rounded-lg text-sm w-full ${
          isSelected
            ? 'bg-bronze-700 text-white dark:bg-bronze-800 ring-2 ring-bronze-600 dark:ring-tango-500'
            : 'bg-gradient-to-r from-tango-100 to-fire-100 text-bronze-700 hover:from-tango-200 hover:to-fire-200 dark:from-tango-900/30 dark:to-fire-900/30 dark:text-tango-200 dark:hover:from-tango-900/40 dark:hover:to-fire-900/40'
        } transition-colors shadow-sm hover:shadow`}
      >
        <ModelIcon model="openrouter/auto" provider="openrouter" size={24} className="mr-2 flex-shrink-0" />
        <div>
          <span className="font-medium">AutoRouter</span>
          <p className="text-xs opacity-80 text-left">Sélectionne automatiquement le meilleur modèle pour votre requête</p>
        </div>
      </button>
    </div>
  );
};

export default AutoRouterButton;
