{"name": "roo-mcp-server", "version": "0.1.0", "description": "MCP server for Roo Code Memory Bank functionality", "type": "module", "main": "dist/index.js", "bin": {"roo-mcp-server": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "start": "node dist/index.js", "watch": "tsc --watch", "prepare": "npm run build"}, "keywords": ["mcp", "memory", "context"], "author": "", "license": "Apache-2.0", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "chalk": "^5.4.1", "yargs": "^17.7.2"}, "devDependencies": {"@types/node": "^22.15.3", "@types/yargs": "^17.0.33", "shx": "^0.3.4", "typescript": "^5.8.3"}}