import React, { useState } from 'react';
import MarkdownRenderer from '../../common/MarkdownRenderer';
import { Reference } from './AiMessage/types';

/**
 * Composant de démonstration pour le rendu Markdown
 */
const MarkdownDemo: React.FC = () => {
  const [demoContent] = useState(`
# Démonstration du rendu Markdown

Ce composant démontre les capacités de notre moteur de rendu Markdown.

## Formatage de texte

**Texte en gras** et *texte en italique* sont pris en charge.

~~Texte barré~~ est également disponible.

## Listes

### Listes non ordonnées
- Élément 1
- Élément 2
  - Élément imbriqué 1
  - Élément imbriqué 2
- Élément 3

### Listes ordonnées
1. Premier élément
2. Deuxième élément
   1. Élément imbriqué 1
   2. Élément imbriqué 2
3. Troisième élément

### Listes de tâches
- [x] Tâche terminée
- [ ] Tâche incomplète
- [x] Autre tâche terminée

## Blocs de code

Code en ligne: \`const salutation = "Bonjour, monde!";\`

\`\`\`javascript
// Code JavaScript avec coloration syntaxique
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10)); // Sortie: 55
\`\`\`

\`\`\`python
# Code Python avec coloration syntaxique
def fibonacci(n):
    a, b = 0, 1
    for _ in range(n):
        a, b = b, a + b
    return a

print(fibonacci(10))  # Sortie: 55
\`\`\`

## Tableaux

| Nom      | Type    | Description                |
|----------|---------|----------------------------|
| id       | string  | Identifiant unique         |
| nom      | string  | Nom complet de l'utilisateur |
| age      | number  | Âge de l'utilisateur       |
| estActif | boolean | Si l'utilisateur est actif |

## Citations

> Ceci est une citation.
>
> Elle peut s'étendre sur plusieurs lignes.
>
> > Et peut être imbriquée.

## Références

Ce texte a une référence [1] et une autre référence [2].

`);

  // Références de démonstration
  const demoReferences: Reference[] = [
    {
      id: '1',
      title: 'Exemple de référence 1',
      url: 'https://example.com/ref1',
    },
    {
      id: '2',
      title: 'Exemple de référence 2',
      url: 'https://example.com/ref2',
    },
  ];

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Démonstration du rendu Markdown</h2>
      <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg">
        <MarkdownRenderer
          content={demoContent}
          references={demoReferences}
          isStreaming={false}
          showCursor={false}
        />
      </div>
    </div>
  );
};

export default MarkdownDemo;
