import { useState, useRef } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { Id } from "../../../../../../convex/_generated/dataModel";

/**
 * Hook personnalisé pour gérer l'état du chat
 * @param onConversationCreated - Fonction appelée lorsqu'une nouvelle conversation est créée
 */
export const useChat = (onConversationCreated?: (id: Id<"conversations">) => void) => {
  // État du chat
  const [userMessage, setUserMessage] = useState("");
  const [chatStarted, setChatStarted] = useState(false);
  const [assistantResponse, setAssistantResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null>(null);
  const [activeButton, setActiveButton] = useState("new"); // 'new', 'chat', ou 'settings'

  // Références
  const bottomRef = useRef<HTMLDivElement>(null);

  // Mutations Convex
  const createConversation = useMutation(api.conversations.create);
  const sendMessage = useMutation(api.messages.send);

  // Récupérer les conversations de l'utilisateur
  const conversations = useQuery(api.conversations.list) || [];

  // Gérer les clics sur les boutons de la sidebar
  const handleSidebarClick = (buttonType: string) => {
    setActiveButton(buttonType);

    if (buttonType === "new") {
      // Démarrer une nouvelle conversation
      setChatStarted(false);
      setUserMessage("");
      setAssistantResponse("");
      setCurrentConversationId(null);
    } else if (buttonType === "chat") {
      // Afficher l'historique des conversations (simulé)
      if (!chatStarted) {
        setChatStarted(true);
        setAssistantResponse("Voici l'historique de vos conversations. Vous pouvez reprendre une conversation ou en démarrer une nouvelle.");
      }
    } else if (buttonType === "settings") {
      // Afficher les paramètres (simulé)
      setChatStarted(true);
      setAssistantResponse("Paramètres de l'application : Vous pouvez configurer vos préférences ici.");
    }
  };

  // Gérer la sélection d'une conversation
  const handleSelectConversation = (conversationId: Id<"conversations">) => {
    setCurrentConversationId(conversationId);
    setChatStarted(true);
    setActiveButton("chat");

    // Rediriger vers la page de conversation si la fonction est fournie
    if (onConversationCreated) {
      onConversationCreated(conversationId);
    }
  };

  // Gérer la création d'une nouvelle conversation
  const handleNewConversation = () => {
    setCurrentConversationId(null);
    setChatStarted(false);
    setUserMessage("");
    setAssistantResponse("");
    setActiveButton("new");
  };

  // Gérer les touches pressées dans le textarea
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (userMessage.trim() && !isLoading) {
        startChat();
      }
    }
  };

  // Fonction pour arrêter la génération
  const stopGeneration = () => {
    console.log("🛑 [useChat] Arrêt de la génération demandé");
    setIsLoading(false);
    setIsStreaming(false);
    // TODO: Implémenter l'arrêt côté backend si nécessaire
  };

  // Démarrer un chat
  const startChat = async (modelId?: string) => {
    if (!userMessage.trim() || isLoading) return;

    console.log("🔍 [useChat] startChat appelé avec modelId:", modelId);

    setIsLoading(true);
    setIsStreaming(true);
    // Ne pas déclencher chatStarted immédiatement pour éviter l'affichage temporaire
    // setChatStarted(true);

    try {
      let conversationId = currentConversationId;

      // Convertir "autoselect" en "openrouter/auto"
      const finalModelId = modelId === "autoselect" ? "openrouter/auto" : (modelId || "openrouter/auto");
      const isAutoRouter = modelId === "autoselect" || !modelId || modelId === "openrouter/auto";

      console.log("🔍 [useChat] Conversion modelId:", {
        original: modelId,
        final: finalModelId,
        isAutoRouter: isAutoRouter
      });

      // Créer une nouvelle conversation si nécessaire
      if (!conversationId) {
        conversationId = await createConversation({
          title: userMessage.slice(0, 50) + (userMessage.length > 50 ? "..." : ""),
          modelId: finalModelId,
          usesAutoRouter: isAutoRouter
        });
        setCurrentConversationId(conversationId);

        // Appeler la fonction de callback si fournie
        if (onConversationCreated) {
          onConversationCreated(conversationId);
        }
      }

      // Envoyer le message
      await sendMessage({
        conversationId,
        content: userMessage,
        modelId: finalModelId
      });

      // Réinitialiser le message
      setUserMessage("");
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
      // Ne pas afficher de message d'erreur à l'utilisateur
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  return {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    isStreaming,
    currentConversationId,
    activeButton,
    setActiveButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat,
    stopGeneration
  };
};
