/* Styles pour le contenu Markdown */

/* Styles généraux */
.markdown-content {
  overflow-wrap: break-word;
  word-break: break-word;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Couleur blanche par défaut sauf pour les blocs de code */
.markdown-content:not(pre):not(pre *) {
  color: white !important;
}

/* Forcer la couleur blanche pour tous les éléments de texte sauf le code */
.markdown-content p:not(pre p),
.markdown-content li:not(pre li),
.markdown-content span:not(pre span):not(.token),
.markdown-content div:not(pre div):not([class*="language-"]),
.markdown-content h1:not(pre h1),
.markdown-content h2:not(pre h2),
.markdown-content h3:not(pre h3),
.markdown-content h4:not(pre h4),
.markdown-content h5:not(pre h5),
.markdown-content h6:not(pre h6),
.markdown-content strong:not(pre strong),
.markdown-content b:not(pre b) {
  color: white !important;
}

/* Styles spécifiques pour les éléments en gras (sauf dans le code) */
.markdown-content strong:not(pre strong),
.markdown-content b:not(pre b),
.markdown-content th strong:not(pre strong),
.markdown-content th b:not(pre b),
.markdown-content td strong:not(pre strong),
.markdown-content td b:not(pre b) {
  font-weight: 700 !important;
  color: white !important;
}

/* Empêcher les sauts de ligne multiples */
.markdown-content br + br,
.markdown-content br + br + br,
.markdown-content p:empty,
.markdown-content p:empty + p:empty {
  display: none;
}

/* Améliorer l'espacement des listes */
.markdown-content li {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}

/* Éviter les sauts de ligne après les tirets dans les listes */
.markdown-content li::marker {
  content: "- ";
}

.markdown-content ul > li {
  list-style: none;
  position: relative;
}

.markdown-content ul > li::before {
  content: "-";
  position: absolute;
  left: -1rem;
  color: white;
}

/* Réduire l'espacement entre les paragraphes */
.markdown-content p + p {
  margin-top: -0.25rem;
}

/* Normalisation de l'espacement vertical - version compacte */
.markdown-content > * {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.markdown-content > *:last-child {
  margin-bottom: 0;
}

/* Styles pour les titres */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  color: white !important;
}

.markdown-content h1 {
  font-size: 1.5rem;
}

.markdown-content h2 {
  font-size: 1.25rem;
}

.markdown-content h3 {
  font-size: 1.125rem;
}

.markdown-content h4 {
  font-size: 1rem;
}

/* Styles pour les paragraphes */
.markdown-content p {
  margin-top: 0;
  margin-bottom: 0.5rem;
  line-height: 1.4;
  color: white !important;
}

/* Styles pour les listes */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: white !important;
}

.markdown-content li {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
  color: white !important;
}

.markdown-content li > p {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
  color: white !important;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

/* Styles pour les listes de tâches */
.markdown-content ul.contains-task-list {
  list-style-type: none;
  padding-left: 0.5rem;
}

.markdown-content .task-list-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Styles pour les liens */
.markdown-content a {
  color: #e37314;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Styles pour les citations */
.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 0.5rem 0;
  color: white !important;
}

.dark .markdown-content blockquote {
  border-left-color: #4a5568;
  color: white !important;
}

/* Styles pour les images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 0.5rem 0;
  border-radius: 0.375rem;
}

/* Styles pour les références */
.markdown-content .reference-link {
  color: #e37314;
  font-weight: 500;
}

.markdown-content .reference-link:hover {
  text-decoration: underline;
}

/* Styles pour les tableaux */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 0.5rem 0;
}

.markdown-content table th,
.markdown-content table td {
  padding: 0.375rem 0.5rem;
  text-align: left;
  color: white !important;
}

.markdown-content table th {
  font-weight: 700 !important;
  color: white !important;
  background-color: #374151 !important; /* gray-700 */
}

.markdown-content table td {
  color: white !important;
}

/* Styles pour le code */
.markdown-content pre {
  margin: 0.5rem 0;
  border-radius: 0.375rem;
  overflow: hidden;
}

/* Ne pas forcer les couleurs sur les blocs de code avec SyntaxHighlighter */
.markdown-content pre code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  background: none !important;
  color: inherit !important;
}

/* Styles seulement pour le code en ligne (pas dans les blocs pre) */
.markdown-content :not(pre) > code {
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: #374151 !important; /* gray-700 */
  color: #e5e7eb !important; /* gray-200 */
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* Styles pour les séparateurs horizontaux */
.markdown-content hr {
  margin: 0.75rem 0;
  border: 0;
  border-top: 1px solid #e2e8f0;
}

.dark .markdown-content hr {
  border-top-color: #4a5568;
}

/* Styles pour les diagrammes Mermaid */
.markdown-content .mermaid {
  text-align: center;
  margin: 0.5rem 0;
  background-color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.dark .markdown-content .mermaid {
  background-color: #1a202c;
}

/* Styles spécifiques pour surcharger Tailwind prose (sauf code) */
.prose .markdown-content th:not(pre th),
.prose .markdown-content td:not(pre td),
.prose .markdown-content strong:not(pre strong),
.prose .markdown-content b:not(pre b),
.prose-invert .markdown-content th:not(pre th),
.prose-invert .markdown-content td:not(pre td),
.prose-invert .markdown-content strong:not(pre strong),
.prose-invert .markdown-content b:not(pre b) {
  color: white !important;
  font-weight: 700 !important;
}

/* Styles encore plus spécifiques pour les tableaux dans prose */
.prose .markdown-content table th,
.prose .markdown-content table td,
.prose-invert .markdown-content table th,
.prose-invert .markdown-content table td {
  color: white !important;
}

.prose .markdown-content table th,
.prose-invert .markdown-content table th {
  font-weight: 700 !important;
}

/* Permettre au SyntaxHighlighter de fonctionner correctement */
.markdown-content pre[class*="language-"],
.markdown-content pre[class*="language-"] *,
.markdown-content .hljs,
.markdown-content .hljs *,
.markdown-content pre div[style],
.markdown-content pre div[style] *,
.markdown-content pre span,
.markdown-content pre code,
.markdown-content pre code *,
.markdown-content div[class*="language-"],
.markdown-content div[class*="language-"] * {
  color: inherit !important;
  background: inherit !important;
}

/* Surcharger TOUS les styles qui pourraient affecter le code */
.markdown-content pre *,
.markdown-content pre,
.markdown-content code[class*="language-"],
.markdown-content code[class*="language-"] * {
  color: inherit !important;
  background: inherit !important;
}

/* Styles spécifiques pour react-syntax-highlighter */
.markdown-content pre > div[style] {
  background: inherit !important;
}

.markdown-content pre > div[style] * {
  color: inherit !important;
}

/* Exclure complètement les blocs de code des styles globaux */
.markdown-content pre,
.markdown-content pre *,
.markdown-content .token,
.markdown-content .token *,
.code-block-container,
.code-block-container * {
  color: inherit !important;
  background: inherit !important;
}

/* Styles spécifiques pour les conteneurs de blocs de code */
.code-block-container {
  color: initial !important;
  background: #1e1e1e !important; /* Rétablir la boîte noire */
  border-radius: 0.375rem;
  overflow: hidden;
  margin: 0.25rem 0;
}

.code-block-container * {
  color: initial !important;
  background: inherit !important;
}

/* Surcharger spécifiquement les styles Tailwind prose pour les blocs de code */
.prose .code-block-container,
.prose-invert .code-block-container,
.prose .code-block-container *,
.prose-invert .code-block-container *,
.prose .code-block-container pre,
.prose-invert .code-block-container pre,
.prose .code-block-container code,
.prose-invert .code-block-container code,
.prose .code-block-container span,
.prose-invert .code-block-container span,
.prose .code-block-container div,
.prose-invert .code-block-container div {
  color: initial !important;
  background: inherit !important;
}

/* Styles encore plus spécifiques pour les tokens de code */
.prose .token,
.prose-invert .token,
.prose .hljs,
.prose-invert .hljs,
.prose .hljs *,
.prose-invert .hljs * {
  color: inherit !important;
  background: inherit !important;
}

/* Surcharger TOUS les styles Tailwind prose pour le code */
.prose pre,
.prose-invert pre,
.prose pre code,
.prose-invert pre code,
.prose code,
.prose-invert code {
  color: inherit !important;
  background-color: inherit !important;
}

/* Styles ultra-spécifiques pour surcharger Tailwind prose */
.prose.markdown-content pre,
.prose-invert.markdown-content pre,
.prose.markdown-content pre *,
.prose-invert.markdown-content pre *,
.prose.markdown-content code,
.prose-invert.markdown-content code,
.prose.markdown-content .code-block-container,
.prose-invert.markdown-content .code-block-container,
.prose.markdown-content .code-block-container *,
.prose-invert.markdown-content .code-block-container * {
  color: inherit !important;
  background: inherit !important;
}

/* Forcer l'héritage pour tous les éléments dans les blocs de code */
.prose .code-block-container [style],
.prose-invert .code-block-container [style],
.prose .code-block-container [style] *,
.prose-invert .code-block-container [style] * {
  color: inherit !important;
  background: inherit !important;
}
