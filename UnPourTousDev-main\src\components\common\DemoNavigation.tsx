import React from "react";
import { Link } from "react-router-dom";

/**
 * Simple navigation component for demo pages
 */
const DemoNavigation: React.FC = () => {
  return (
    <div className="fixed top-0 left-0 right-0 bg-surface-light dark:bg-surface-dark p-2 z-50 flex justify-center border-b border-gray-200 dark:border-gray-800">
      <div className="flex space-x-4">
        <Link
          to="/"
          className="px-3 py-1 rounded-md bg-primary text-white hover:opacity-90 transition-colors"
        >
          Main App
        </Link>
        <Link
          to="/demo"
          className="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          Markdown Demo
        </Link>
        <Link
          to="/newconversation"
          className="px-3 py-1 rounded-md bg-claude-orange text-white hover:opacity-90 transition-colors"
        >
          New Conversation Demo
        </Link>
        <Link
          to="/claude"
          className="px-3 py-1 rounded-md bg-primary text-white hover:opacity-90 transition-colors"
        >
          Interface Un Pour Tous
        </Link>
      </div>
    </div>
  );
};

export default DemoNavigation;
