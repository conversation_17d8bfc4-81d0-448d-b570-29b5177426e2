<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="23.0787 15.8298 128.326 141.9005" width="128.326px" height="141.9px">
  <path d="M 86.941 36.386 C 85.866 36.53 84.773 36.865 83.825 37.342 C 83.16 37.676 79.199 40.69 73.959 44.847 C 73.245 45.414 71.776 46.554 70.695 47.381 C 67.358 49.933 63.913 52.585 61.934 54.124 C 60.893 54.934 59.47 56.027 58.771 56.554 C 56.8 58.039 54.849 59.747 54.373 60.406 C 54.134 60.734 53.789 61.377 53.605 61.835 C 53.283 62.635 53.269 62.858 53.224 67.893 C 53.172 73.554 53.252 74.522 53.881 75.824 C 54.315 76.725 55.532 78.009 56.463 78.549 C 57.646 79.235 58.615 79.394 62.103 79.474 C 63.885 79.515 65.522 79.617 65.741 79.7 C 66.771 80.094 68.191 82.161 68.412 83.59 C 68.463 83.921 68.51 89.907 68.516 96.892 L 68.527 109.592 L 68.1 110.502 C 67.598 111.573 66.377 112.961 65.469 113.493 C 64.284 114.188 63.637 114.297 60.648 114.309 C 56.031 114.327 54.738 114.591 53.147 115.839 C 52.064 116.689 51.361 117.654 50.955 118.848 C 50.64 119.774 50.628 120.039 50.576 126.919 C 50.517 134.714 50.577 135.57 51.283 137.087 C 51.85 138.302 53.133 139.567 54.468 140.223 L 55.47 140.717 L 89.05 140.762 C 114.366 140.796 122.829 140.764 123.44 140.634 C 125.651 140.161 127.501 138.446 128.254 136.171 C 128.471 135.513 128.511 134.499 128.559 128.353 C 128.594 123.862 128.557 120.859 128.457 120.106 C 128.246 118.509 127.632 117.313 126.474 116.241 C 124.957 114.836 123.958 114.509 120.553 114.303 C 118.803 114.197 118.347 114.118 117.65 113.799 C 116.509 113.276 115.485 112.273 114.884 111.088 L 114.383 110.101 L 114.308 76.075 C 114.238 44.212 114.217 41.994 113.975 41.19 C 113.653 40.115 113.53 39.878 112.799 38.918 C 112.052 37.936 111.013 37.172 109.765 36.687 L 108.787 36.307 L 98.331 36.284 C 92.58 36.272 87.455 36.318 86.941 36.386 M 50.611 127.684 C 50.611 131.531 50.63 133.105 50.653 131.181 C 50.676 129.258 50.676 126.11 50.653 124.186 C 50.63 122.263 50.611 123.837 50.611 127.684" stroke="none" fill="#e37313" fill-rule="evenodd" style="stroke-width: 0.705;" id="path-2">
    <title>UN</title>
  </path>
  <path d="M 75.965 60.199 C 74.885 60.689 74.22 61.323 73.72 62.338 C 73.289 63.213 73.259 63.37 73.322 64.428 C 73.402 65.751 73.717 66.476 74.568 67.29 C 75.538 68.216 76.289 68.517 77.637 68.517 C 78.666 68.517 78.914 68.462 79.6 68.082 C 81.631 66.957 82.43 64.545 81.468 62.443 C 80.961 61.334 80.48 60.841 79.404 60.325 C 78.301 59.797 76.959 59.748 75.965 60.199 M 98.368 60.145 C 97.475 60.462 96.372 61.512 95.96 62.438 C 95.448 63.589 95.469 65.003 96.014 66.114 C 97.647 69.439 102.261 69.36 103.86 65.979 C 104.206 65.247 104.245 65.016 104.186 64.035 C 104.08 62.252 103.35 61.111 101.833 60.352 C 101.088 59.98 100.763 59.904 99.961 59.913 C 99.427 59.92 98.71 60.024 98.368 60.145 M 76.861 75.28 C 76.357 75.742 76.311 75.85 76.311 76.574 C 76.311 77.306 76.377 77.456 77.192 78.588 C 79.221 81.406 82.063 83.394 85.275 84.241 C 86.999 84.696 90.38 84.688 92.069 84.225 C 94.529 83.55 96.776 82.205 98.567 80.335 C 100.173 78.659 100.979 77.422 100.977 76.637 C 100.976 75.59 100.234 74.792 99.255 74.781 C 98.47 74.772 98.057 75.072 97.236 76.242 C 95.225 79.113 92.316 80.758 88.956 80.924 C 87.643 80.989 87.296 80.951 86.084 80.605 C 83.574 79.888 81.435 78.333 79.95 76.147 C 79.183 75.017 78.847 74.776 78.044 74.776 C 77.529 74.776 77.309 74.869 76.861 75.28" stroke="none" fill-rule="evenodd" style="stroke-width: 0.705;" id="path-3">
    <title>EYES</title>
  </path>
  <path d="M 44.873 143.321 L 49.168 133.476 C 50.206 131.093 52.106 129.192 54.487 128.158 L 64.348 123.863 C 65.109 123.525 65.109 122.439 64.348 122.114 L 54.487 117.82 C 52.106 116.785 50.206 114.881 49.168 112.501 L 44.873 102.642 C 44.538 101.878 43.449 101.878 43.128 102.642 L 38.83 112.501 C 37.795 114.881 35.894 116.785 33.514 117.82 L 23.642 122.128 C 22.878 122.464 22.878 123.538 23.642 123.874 L 33.681 128.313 C 36.063 129.362 37.95 131.276 38.974 133.656 L 43.139 143.321 C 43.477 144.085 44.563 144.099 44.887 143.321 L 44.873 143.321 Z" style="stroke-width: 1; fill: rgb(63, 140, 255); transform-origin: 43.9939px 122.984px;" id="object-2" transform="matrix(0, 1, -1, 0, 0, 0.00001526)"/>
  <path d="M 150.666 48.289 L 137.948 42.738 C 134.871 41.4 132.422 38.942 131.083 35.874 L 125.533 23.156 C 125.104 22.169 123.704 22.169 123.271 23.156 L 117.722 35.874 C 116.384 38.951 113.927 41.4 110.858 42.738 L 98.124 48.296 C 97.137 48.727 97.137 50.118 98.124 50.549 L 111.077 56.278 C 114.145 57.632 116.587 60.098 117.91 63.183 L 123.28 75.659 C 123.704 76.653 125.111 76.653 125.542 75.659 L 131.083 62.964 C 132.422 59.887 134.88 57.436 137.948 56.099 L 150.666 50.549 C 151.651 50.118 151.651 48.718 150.666 48.289 Z" style="stroke-width: 1; fill: rgb(63, 140, 255); transform-origin: 124.394px 49.4101px;" id="object-3"/>
  <path d="M 99.725 40.783 L 102.324 34.829 C 102.951 33.386 104.101 32.236 105.54 31.611 L 111.506 29.013 C 111.966 28.809 111.966 28.152 111.506 27.956 L 105.54 25.358 C 104.101 24.731 102.951 23.58 102.324 22.14 L 99.725 16.177 C 99.522 15.714 98.863 15.714 98.669 16.177 L 96.07 22.14 C 95.444 23.58 94.295 24.731 92.854 25.358 L 86.881 27.965 C 86.421 28.168 86.421 28.817 86.881 29.021 L 92.954 31.705 C 94.395 32.339 95.538 33.497 96.156 34.936 L 98.676 40.783 C 98.881 41.246 99.538 41.253 99.734 40.783 L 99.725 40.783 Z" style="stroke-width: 1; fill: rgb(63, 140, 255); transform-origin: 99.1935px 28.4813px;" id="object-4"/>
  <path d="M 53.394 147.092 L 58.724 149.447 C 59.993 150.005 60.988 151.026 61.532 152.281 L 63.743 157.419 C 63.924 157.833 64.494 157.833 64.676 157.419 L 66.952 152.191 C 67.508 150.922 68.518 149.912 69.773 149.371 L 75.001 147.092 C 75.401 146.912 75.401 146.342 75.001 146.162 L 69.773 143.883 C 68.506 143.328 67.497 142.318 66.952 141.063 L 64.676 135.835 C 64.494 135.435 63.924 135.435 63.743 135.835 L 61.467 141.063 C 60.909 142.332 59.902 143.342 58.645 143.883 L 53.405 146.173 C 53.005 146.356 53.005 146.926 53.405 147.106 L 53.394 147.092 Z" style="stroke-width: 1; fill: rgb(63, 140, 255); transform-origin: 64.203px 146.632px;" id="object-5" transform="matrix(0, 1, -1, 0, 0.00000763, 0.00000763)"/>
</svg>