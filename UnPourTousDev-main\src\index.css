/* Import markdown styles */
@import './styles/markdown.css';
@import './styles/katex.css';
@import './styles/mermaid.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles personnalisés pour les barres de défilement - Appliqués globalement */

/* --- Styles WebKit (Chrome, Safari, Edge, etc.) --- */
::-webkit-scrollbar-button {
  display: none; /* Masquer les boutons fléchés */
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3); /* gray-400 avec opacité */
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5); /* gray-400 avec plus d'opacité au survol */
  background-clip: content-box;
}

/* Version pour le mode sombre */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.3); /* gray-500 avec opacité */
  background-clip: content-box;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.5); /* gray-500 avec plus d'opacité au survol */
  background-clip: content-box;
}

/* --- Styles Firefox --- */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent; /* pouce / piste */
}

.dark * {
  scrollbar-color: rgba(107, 114, 128, 0.3) transparent; /* pouce / piste en mode sombre */
}

@layer base {
  body {
    @apply bg-surface-light text-text-light transition-colors duration-200;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }

  .dark body {
    @apply bg-surface-dark text-text-dark;
  }

  /* Définir les couleurs de base pour le mode sombre par défaut */
  :root {
    color-scheme: light dark;
  }
}

@layer components {
  .input-field {
    @apply w-full border border-gray-300 dark:border-border-dark rounded-lg bg-surface-light dark:bg-surface-dark text-text-light dark:text-text-dark px-3 py-2;
  }

  .auth-button {
    @apply w-full bg-primary text-white rounded-lg hover:opacity-90 transition-colors disabled:opacity-50 px-4 py-2;
  }

  .card {
    @apply bg-surface-light dark:bg-surface-dark rounded-lg shadow-md transition-colors duration-200 h-auto;
    overflow: visible;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-border-dark;
  }

  .card-content {
    @apply p-6 h-auto;
    min-height: auto;
    max-height: none;
    overflow: visible;
  }

  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-primary text-white hover:opacity-90 dark:bg-primary dark:hover:opacity-80;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-secondary dark:text-text-dark dark:hover:bg-secondary-light;
  }

  /* Toggle switch styles */
  .toggle-container {
    @apply relative inline-block w-12 h-6;
  }

  .toggle-input {
    @apply opacity-0 w-0 h-0;
  }

  .toggle-bg {
    @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 dark:bg-border-dark rounded-full transition-colors duration-200;
  }

  .toggle-bg:before {
    @apply absolute content-[''] h-5 w-5 left-0.5 bottom-0.5 bg-white rounded-full transition-transform duration-200;
  }

  .toggle-input:checked + .toggle-bg {
    @apply bg-primary dark:bg-primary;
  }

  .toggle-input:checked + .toggle-bg:before {
    @apply transform translate-x-6;
  }
}


