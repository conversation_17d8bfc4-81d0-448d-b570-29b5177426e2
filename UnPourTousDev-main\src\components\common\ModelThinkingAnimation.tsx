import React from 'react';
import { motion } from 'framer-motion';

interface ModelThinkingAnimationProps {
  isThinking: boolean;
  modelIcon?: React.ReactNode;
  modelName?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Animation de "réflexion" pour les modèles généraux
 * Selon la documentation : "Modèles généraux : Animation de 'réflexion' derrière le logo"
 */
const ModelThinkingAnimation: React.FC<ModelThinkingAnimationProps> = ({
  isThinking,
  modelIcon,
  modelName,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const pulseAnimation = {
    scale: [1, 1.1, 1],
    opacity: [0.7, 1, 0.7],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  const backgroundPulse = {
    scale: [1, 1.3, 1],
    opacity: [0.3, 0.6, 0.3],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
      delay: 0.2
    }
  };

  return (
    <div className={`relative flex items-center justify-center ${className}`}>
      {/* Animation de fond pulsante */}
      {isThinking && (
        <motion.div
          className="absolute inset-0 bg-blue-500/20 rounded-full"
          animate={backgroundPulse}
        />
      )}
      
      {/* Icône du modèle avec animation */}
      <motion.div
        className={`${sizeClasses[size]} flex items-center justify-center relative z-10`}
        animate={isThinking ? pulseAnimation : {}}
      >
        {modelIcon || (
          <div className="w-full h-full bg-gray-600 rounded-full flex items-center justify-center">
            <span className="text-xs font-bold text-white">
              {modelName?.charAt(0) || 'M'}
            </span>
          </div>
        )}
      </motion.div>

      {/* Points de réflexion animés */}
      {isThinking && (
        <div className="absolute -bottom-6 flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-1 h-1 bg-gray-400 rounded-full"
              animate={{
                opacity: [0.3, 1, 0.3],
                y: [0, -2, 0]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ModelThinkingAnimation;
