import React from 'react';
import { Conversation } from '../../ComfyInterface/types';
import { Id } from '../../../../../../convex/_generated/dataModel';
import ConversationItem from './ConversationItem';

interface RecentConversationsProps {
  conversations: Conversation[];
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  visualState: boolean;
  formatDate: (date: number) => string;
  hasMoreConversations: boolean;
  isLoadingMore: boolean;
  handleShowMore: () => void;
  conversationListRef: React.RefObject<HTMLDivElement>;

  // États pour les menus contextuels
  menuOpenId: Id<"conversations"> | null;
  isRenaming: Id<"conversations"> | null;
  newTitle: string;
  confirmDelete: Id<"conversations"> | null;

  // Références
  renameInputRef: React.RefObject<HTMLInputElement>;

  // Fonctions pour les menus contextuels
  handleOpenConversationMenu: (e: React.MouseEvent, conversationId: Id<"conversations">) => void;
  handleCloseConversationMenu: () => void;
  handleStartRename: (conversationId: Id<"conversations">, title: string) => void;
  handleRename: (e: React.FormEvent, conversationId: Id<"conversations">) => void;
  setNewTitle: (title: string) => void;
  handleCancelRename: () => void;
  handleConfirmDelete: (conversationId: Id<"conversations">) => void;
  handleDelete: (conversationId: Id<"conversations">) => void;
  handleCancelDelete: () => void;
}

const RecentConversations: React.FC<RecentConversationsProps> = ({
  conversations,
  currentConversationId,
  onSelectConversation,
  visualState,
  formatDate,
  hasMoreConversations,
  isLoadingMore,
  handleShowMore,
  conversationListRef,
  menuOpenId,
  isRenaming,
  newTitle,
  confirmDelete,
  renameInputRef,
  handleOpenConversationMenu,
  handleCloseConversationMenu,
  handleStartRename,
  handleRename,
  setNewTitle,
  handleCancelRename,
  handleConfirmDelete,
  handleDelete,
  handleCancelDelete
}) => {
  return (
    <div className="w-full overflow-hidden">
      <div
        ref={conversationListRef}
        className="w-full overflow-y-auto max-h-[calc(100vh-200px)] pr-1 conversation-list"
      >
        {!visualState && (
          <h3 className="text-xs font-medium text-gray-400 mb-2 px-2">Récents</h3>
        )}

        {/* Liste des conversations */}
        {conversations.map((conversation: Conversation) => (
          <ConversationItem
            key={conversation._id}
            conversation={conversation}
            currentConversationId={currentConversationId}
            onSelectConversation={onSelectConversation}
            visualState={visualState}
            formatDate={formatDate}
            menuOpenId={menuOpenId}
            isRenaming={isRenaming}
            newTitle={newTitle}
            confirmDelete={confirmDelete}
            renameInputRef={renameInputRef}
            handleOpenConversationMenu={handleOpenConversationMenu}
            handleCloseConversationMenu={handleCloseConversationMenu}
            handleStartRename={handleStartRename}
            handleRename={handleRename}
            setNewTitle={setNewTitle}
            handleCancelRename={handleCancelRename}
            handleConfirmDelete={handleConfirmDelete}
            handleDelete={handleDelete}
            handleCancelDelete={handleCancelDelete}
          />
        ))}

        {/* Bouton "Afficher plus" ou indicateur de chargement */}
        {hasMoreConversations && !visualState && (
          <button
            onClick={handleShowMore}
            disabled={isLoadingMore}
            className="w-full mt-2 py-2 px-3 text-sm text-gray-400 hover:bg-claude-light-gray/20 rounded-lg transition-colors disabled:opacity-50"
          >
            {isLoadingMore ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Chargement...
              </span>
            ) : (
              "Afficher plus"
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default RecentConversations;
