# Refactorisation de UnPourTous

Ce document explique les modifications apportées à la structure du projet UnPourTous dans le cadre de la refactorisation.

## Nouvelle Structure de Dossiers

La nouvelle structure de dossiers suit les guidelines modernes pour les applications React/TypeScript avec Convex et Clerk :

```
/UnPourTous-Refactorised
├── convex/                     # Convex Backend
│   ├── schema.ts
│   ├── auth.config.ts          # Pour l'intégration avec Clerk
│   ├── (functions: ai.ts, messages.ts, users.ts, etc.)
│   └── _generated/             # Ne pas modifier
├── public/                     # Fichiers statiques
├── src/                        # Frontend React/TypeScript
│   ├── assets/                 # Images, icônes, etc.
│   ├── components/             # Composants React
│   │   ├── common/             # Composants communs réutilisables
│   │   ├── layout/             # Composants de mise en page
│   │   ├── features/           # Composants spécifiques aux fonctionnalités
│   │   │   ├── auth/           # Composants liés à l'authentification
│   │   │   ├── chat/           # Composants liés au chat
│   │   │   ├── folders/        # Composants liés aux dossiers
│   │   │   ├── models/         # Composants liés aux modèles
│   │   │   └── settings/       # Composants liés aux paramètres
│   ├── config/                 # Configuration (constantes, etc.)
│   ├── contexts/               # Contextes React
│   ├── hooks/                  # Hooks personnalisés
│   ├── lib/                    # Utilitaires
│   ├── pages/                  # Composants de pages
│   ├── styles/                 # CSS global
│   ├── types/                  # Définitions TypeScript
│   ├── App.tsx                 # Composant racine
│   └── main.tsx                # Point d'entrée
```

## Modifications Apportées

### 1. Réorganisation des Composants

Les composants ont été réorganisés selon leur fonction :

- **common/** : Composants génériques réutilisables (ContextMenu, SpotlightBox, etc.)
- **layout/** : Composants de mise en page (ChatLayout, Sidebar)
- **features/** : Composants spécifiques aux fonctionnalités, organisés par domaine

### 2. Création de Types TypeScript

Des types TypeScript ont été créés pour les entités principales :

- Conversation
- Message
- Model
- Folder
- User

### 3. Centralisation de la Configuration

La configuration a été centralisée dans le dossier `config/` :

- constants.ts : Constantes de l'application
- modelCategories.ts : Configuration des catégories de modèles
- clerk-config.ts : Configuration de Clerk

### 4. Fichiers d'Index pour les Exports

Des fichiers index.ts ont été créés dans chaque dossier de composants pour faciliter les imports :

```typescript
// Exemple: src/components/features/chat/index.ts
export { default as AiMessage } from './AiMessage';
export { default as ChatArea } from './ChatArea';
// ...
```

Cela permet d'importer plusieurs composants en une seule ligne :

```typescript
import { AiMessage, ChatArea } from './components/features/chat';
```

## Avantages de la Nouvelle Structure

1. **Meilleure Organisation** : Les fichiers sont organisés de manière logique selon leur fonction.
2. **Modularité** : Les composants sont regroupés par fonctionnalité, ce qui facilite la maintenance.
3. **Séparation des Préoccupations** : Chaque dossier a une responsabilité claire.
4. **Facilité de Navigation** : Il est plus facile de trouver les fichiers dans une structure bien organisée.
5. **Évolutivité** : La structure peut facilement accueillir de nouvelles fonctionnalités.

## Composants Refactorisés

### 1. NewConversationForm

Le composant `NewConversationForm` a été refactorisé en suivant une approche modulaire :

```
src/components/features/chat/NewConversationForm/
├── components/
│   ├── MessageTextarea.tsx     # Zone de texte pour le message
│   ├── ModelButton.tsx         # Bouton pour un modèle individuel
│   ├── ModelCategory.tsx       # Section pour une catégorie de modèles
│   ├── ModelSelector.tsx       # Sélecteur de modèles
│   └── AutoRouterButton.tsx    # Bouton pour l'option AutoRouter
├── hooks/
│   ├── useModelSelection.ts    # Gestion de la sélection des modèles
│   └── useMessageInput.ts      # Gestion de la saisie et de l'envoi des messages
├── types.ts                    # Types pour le formulaire
├── utils.ts                    # Fonctions utilitaires
├── index.ts                    # Exports simplifiés
└── NewConversationForm.tsx     # Composant principal
```

Avantages de cette refactorisation :
- Chaque fichier a une responsabilité unique et bien définie
- La logique métier est extraite dans des hooks personnalisés
- Les composants sont plus petits et plus faciles à maintenir
- Le typage fort avec TypeScript améliore la robustesse

## Prochaines Étapes

1. **Refactorisation des autres composants volumineux** :
   - Sidebar.tsx (39126 lignes)
   - AiMessage.tsx (7519 lignes)
   - FolderItem.tsx (13140 lignes)
   - SettingsPage.tsx (8050 lignes)

2. **Mise à jour des Imports** : Mettre à jour les imports dans tous les fichiers pour refléter la nouvelle structure.

3. **Configuration des Alias de Chemin** : Configurer des alias dans tsconfig.json pour simplifier les imports.

4. **Tests** : S'assurer que l'application fonctionne correctement après la refactorisation.
