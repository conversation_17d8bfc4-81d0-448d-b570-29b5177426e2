/* Styles personnalisés pour le contenu Markdown */

/* Styles pour les tableaux */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
  display: block;
}

.markdown-content table thead {
  background-color: rgba(229, 231, 235, 0.5);
}

.dark .markdown-content table thead {
  background-color: rgba(55, 65, 81, 0.5);
}

.markdown-content table th,
.markdown-content table td {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.dark .markdown-content table th,
.dark .markdown-content table td {
  border-color: #374151;
}

.markdown-content table th {
  font-weight: 600;
}

/* Styles pour les blocs de code */
.markdown-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.markdown-content code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.markdown-content :not(pre) > code {
  background-color: rgba(229, 231, 235, 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.dark .markdown-content :not(pre) > code {
  background-color: rgba(55, 65, 81, 0.5);
  color: #e5e7eb;
}

/* Styles pour les listes */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.markdown-content li {
  margin: 0.25rem 0;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

/* Styles pour les titres */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h1 {
  font-size: 1.875rem;
}

.markdown-content h2 {
  font-size: 1.5rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

.markdown-content h4 {
  font-size: 1.125rem;
}

/* Styles pour les citations */
.markdown-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
}

.dark .markdown-content blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

/* Styles pour les liens */
.markdown-content a {
  color: #e37314; /* Couleur primary */
  text-decoration: none;
}

.markdown-content a:hover {
  color: #c76915; /* Couleur primary-hover */
  text-decoration: underline;
}

/* Styles pour les images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

/* Styles pour les listes de tâches */
.markdown-content input[type="checkbox"] {
  margin-right: 0.5rem;
}

/* Styles pour les éléments en gras - forcer blanc et gras */
.markdown-content strong,
.markdown-content b,
.markdown-content th strong,
.markdown-content th b,
.markdown-content td strong,
.markdown-content td b {
  font-weight: 700 !important;
  color: white !important;
}
