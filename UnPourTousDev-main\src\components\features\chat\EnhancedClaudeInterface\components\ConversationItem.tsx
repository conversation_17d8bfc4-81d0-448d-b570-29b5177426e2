import React, { useRef } from 'react';
import { Conversation } from '../../ClaudeInterface/types';
import { Id } from '../../../../../../convex/_generated/dataModel';
import { ModelIconDisplay } from '../../../../features/models';
import ConversationContextMenu from './ConversationContextMenu';
import RenameForm from './RenameForm';
import DeleteConfirmation from './DeleteConfirmation';

interface ConversationItemProps {
  conversation: Conversation;
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  visualState: boolean;
  formatDate: (date: number) => string;
  
  // États pour les menus contextuels
  menuOpenId: Id<"conversations"> | null;
  isRenaming: Id<"conversations"> | null;
  newTitle: string;
  confirmDelete: Id<"conversations"> | null;
  
  // Références
  renameInputRef: React.RefObject<HTMLInputElement>;
  
  // Fonctions pour les menus contextuels
  handleOpenConversationMenu: (e: React.MouseEvent, conversationId: Id<"conversations">) => void;
  handleCloseConversationMenu: () => void;
  handleStartRename: (conversationId: Id<"conversations">, title: string) => void;
  handleRename: (e: React.FormEvent, conversationId: Id<"conversations">) => void;
  setNewTitle: (title: string) => void;
  handleCancelRename: () => void;
  handleConfirmDelete: (conversationId: Id<"conversations">) => void;
  handleDelete: (conversationId: Id<"conversations">) => void;
  handleCancelDelete: () => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  currentConversationId,
  onSelectConversation,
  visualState,
  formatDate,
  menuOpenId,
  isRenaming,
  newTitle,
  confirmDelete,
  renameInputRef,
  handleOpenConversationMenu,
  handleCloseConversationMenu,
  handleStartRename,
  handleRename,
  setNewTitle,
  handleCancelRename,
  handleConfirmDelete,
  handleDelete,
  handleCancelDelete
}) => {
  // Référence pour le bouton de menu contextuel
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  if (isRenaming === conversation._id) {
    return (
      <RenameForm
        conversationId={conversation._id}
        newTitle={newTitle}
        visualState={visualState}
        renameInputRef={renameInputRef}
        handleRename={handleRename}
        setNewTitle={setNewTitle}
        handleCancelRename={handleCancelRename}
      />
    );
  }

  if (confirmDelete === conversation._id) {
    return (
      <DeleteConfirmation
        conversationId={conversation._id}
        visualState={visualState}
        handleCancelDelete={handleCancelDelete}
        handleDelete={handleDelete}
      />
    );
  }

  return (
    <div className="relative group conversation-item">
      <button
        className={`${
          visualState
            ? "w-12 h-12 mx-auto p-0 flex justify-center"
            : "w-full p-2"
        } mb-2 rounded-md text-left text-xs flex items-center ${
          currentConversationId === conversation._id || menuOpenId === conversation._id
            ? "bg-claude-light-gray/30 text-white"
            : "text-gray-400 hover:bg-claude-light-gray/20"
        }`}
        onClick={() => onSelectConversation(conversation._id)}
        title={conversation.title}
      >
        <div className={`${
          visualState
            ? "w-8 h-8 min-w-8 m-0"
            : "w-8 h-8 min-w-8 mr-2"
        } bg-claude-gray rounded-full flex items-center justify-center`}>
          <ModelIconDisplay conversation={conversation} size={16} />
        </div>
        {!visualState && (
          <div className="flex-1 truncate">
            <div className="font-medium truncate">{conversation.title}</div>
            <div className="text-xs text-gray-500">{formatDate(conversation._creationTime)}</div>
          </div>
        )}
      </button>

      {/* Bouton de menu contextuel (visible uniquement en mode déployé et au survol) */}
      {!visualState && (
        <button
          ref={menuButtonRef}
          className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-md ${
            menuOpenId === conversation._id
              ? "bg-claude-light-gray/50 opacity-100"
              : "bg-claude-gray opacity-0 group-hover:opacity-100"
          } flex items-center justify-center transition-all`}
          onClick={(e) => handleOpenConversationMenu(e, conversation._id)}
          aria-label="Menu de la conversation"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      )}

      {/* Menu contextuel */}
      {menuOpenId === conversation._id && (
        <ConversationContextMenu
          conversationId={conversation._id}
          title={conversation.title}
          isOpen={menuOpenId === conversation._id}
          onClose={handleCloseConversationMenu}
          onRename={handleStartRename}
          onDelete={handleConfirmDelete}
          triggerRef={menuButtonRef}
        />
      )}
    </div>
  );
};

export default ConversationItem;
