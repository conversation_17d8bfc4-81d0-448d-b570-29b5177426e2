import React, { useEffect, useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import UserMessage from "../components/features/chat/UserMessage/UserMessage";
import AiMessage from "../components/features/chat/AiMessage/AiMessage";
import LoadingMessage from "../components/LoadingMessage";
import SendButton from "../components/common/SendButton";

/**
 * Page pour afficher une conversation existante
 */
const ConversationPage: React.FC = () => {
  const { conversationId } = useParams<{ conversationId: string }>();
  const navigate = useNavigate();

  // États
  const [userMessage, setUserMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fonction pour arrêter la génération
  const stopGeneration = () => {
    console.log("🛑 [ConversationPage] Arrêt de la génération demandé");
    setIsLoading(false);
    // TODO: Implémenter l'arrêt côté backend si nécessaire
  };

  // Refs
  const bottomRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Convertir l'ID de conversation en Id<"conversations">
  const typedConversationId = conversationId as Id<"conversations">;

  // Récupérer les détails de la conversation
  const conversation = useQuery(api.conversations.getById, {
    conversationId: typedConversationId
  });

  // Récupérer les messages de la conversation
  const messages = useQuery(api.messages.list, {
    conversationId: typedConversationId
  }) || [];

  // Vérifier s'il y a un message en streaming
  const hasStreamingMessage = messages.some(message => message.isStreaming);

  // Mutation pour envoyer un message
  const sendMessage = useMutation(api.messages.send);

  // Défilement automatique vers le bas
  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Gérer l'état de chargement basé sur le streaming
  useEffect(() => {
    if (hasStreamingMessage && isLoading) {
      // Un message de streaming a commencé, on peut arrêter l'indicateur de chargement
      setIsLoading(false);
    }
  }, [hasStreamingMessage, isLoading]);

  // Focus sur le textarea au chargement
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  // Si la conversation n'existe pas ou n'appartient pas à l'utilisateur, rediriger
  useEffect(() => {
    if (conversation === null) {
      navigate("/");
    }
  }, [conversation, navigate]);

  // Gérer l'envoi d'un message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userMessage.trim() || isLoading) return;

    try {
      setIsLoading(true);

      // Utiliser le même modèle que celui utilisé pour créer la conversation
      const modelId = conversation?.usesAutoRouter
        ? "openrouter/auto"
        : conversation?.modelId || "openrouter/auto";

      // Envoyer le message
      await sendMessage({
        content: userMessage,
        conversationId: typedConversationId,
        modelId: modelId
      });

      // Réinitialiser le message utilisateur
      setUserMessage("");
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
      // En cas d'erreur, arrêter le chargement
      setIsLoading(false);
    }
    // Note: isLoading sera mis à false automatiquement quand le streaming commence
  };

  // Gérer les touches du clavier
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  // Ajuster automatiquement la hauteur du textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = e.target;
    setUserMessage(textarea.value);

    // Réinitialiser la hauteur
    textarea.style.height = "auto";

    // Définir la nouvelle hauteur
    textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
  };

  if (!conversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <>
      {/* Zone de chat */}
      <div className="flex-1 overflow-y-auto p-4 pt-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Messages */}
          {messages.map((message) => (
            message.role === "user" ? (
              <UserMessage key={message._id} message={message} />
            ) : (
              <AiMessage key={message._id} message={message} />
            )
          ))}

          {/* Indicateur de chargement - affiché pendant l'attente de la réponse */}
          {isLoading && (
            <LoadingMessage
              modelName={conversation.usesAutoRouter ? "AutoRouter" : conversation.modelId || ""}
              modelId={conversation.usesAutoRouter ? "openrouter/auto" : conversation.modelId || ""}
            />
          )}

          <div ref={bottomRef} />
        </div>
      </div>

      {/* Zone de saisie */}
      <div className="border-t border-claude-light-gray p-4">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSendMessage} className="relative">
            <textarea
              ref={textareaRef}
              value={userMessage}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Écrivez votre message..."
              className="w-full p-3 pr-12 bg-claude-gray text-white rounded-lg resize-none min-h-[50px] max-h-[200px] focus:outline-none focus:ring-1 focus:ring-primary"
              disabled={isLoading}
            />
            <div className="absolute right-3 bottom-3">
              <SendButton
                type="submit"
                disabled={!userMessage.trim()}
                size="sm"
                isLoading={isLoading}
                onStop={stopGeneration}
                showStopButton={true}
              />
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ConversationPage;
