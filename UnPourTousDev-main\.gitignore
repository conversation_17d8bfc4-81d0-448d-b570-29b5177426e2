# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
/node_modules
*/node_modules

# Build outputs
dist
dist-ssr
*.local
/dist
/dist-ssr

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Admin app - exclude completely for security
/admin/
# Si l'application admin est déjà dans le dépôt, vous devrez la supprimer avec:
# git rm -r --cached admin

# Ignored for the template, you probably want to remove it:
package-lock.json