import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UserProfile } from "@clerk/clerk-react";
import { useTheme } from "../contexts/ThemeContext";
import { dark } from "@clerk/themes";
import InterfacePreference from "./features/settings/InterfacePreference";

export default function SettingsPage() {
  const navigate = useNavigate();
  const { isDarkMode, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<"account" | "app">("account");

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 transition-colors duration-200">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6">
        {/* En-tête avec navigation */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Param<PERSON>tres</h1>
          <button
            onClick={() => navigate("/")}
            className="btn btn-secondary flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Retour au chat
          </button>
        </div>

        {/* Onglets de navigation */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-8">
          <button
            className={`py-4 px-8 font-medium text-sm transition-colors duration-200 ${
              activeTab === "account"
                ? "text-gray-900 border-b-2 border-gray-900 dark:text-gray-200 dark:border-gray-200"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
            onClick={() => setActiveTab("account")}
          >
            Compte
          </button>
          <button
            className={`py-4 px-8 font-medium text-sm transition-colors duration-200 ${
              activeTab === "app"
                ? "text-gray-900 border-b-2 border-gray-900 dark:text-gray-200 dark:border-gray-200"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
            onClick={() => setActiveTab("app")}
          >
            Application
          </button>
        </div>

        {/* Contenu des onglets */}
        {activeTab === "account" && (
          <div className="card" style={{ height: 'auto', minHeight: 'auto', maxHeight: 'none', overflow: 'visible' }}>
            <div className="card-header">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Paramètres du compte</h2>
            </div>
            <div className="card-content" style={{
                height: 'auto',
                minHeight: 'auto',
                maxHeight: 'none',
                width: '100%',
                overflow: 'visible'
              }}>
              {/* Intégration du composant UserProfile de Clerk */}
              <UserProfile
                appearance={{
                  elements: {
                    rootBox: "w-full h-auto",
                    card: "shadow-none p-0 h-auto w-full",
                    navbar: "hidden", // Masquer la barre de navigation Clerk
                    pageScrollBox: "p-0 h-auto w-full overflow-visible",
                    profilePage: {
                      root: "h-auto w-full overflow-visible",
                      scrollBox: "h-auto w-full overflow-visible",
                    },
                    profileSection: {
                      root: "h-auto w-full overflow-visible",
                      content: "h-auto w-full overflow-visible",
                    },
                    profileSectionContent: "h-auto w-full overflow-visible",
                    profileSectionPrimaryContent: "h-auto w-full overflow-visible",
                    profileSectionSecondaryContent: "h-auto w-full overflow-visible",
                    accordionContent: "h-auto w-full overflow-visible",
                    accordionTriggerButton: "h-auto w-full",
                  },
                  layout: {
                    socialButtonsPlacement: "bottom",
                    socialButtonsVariant: "iconButton",
                    termsPageUrl: "https://clerk.com/terms",
                    privacyPageUrl: "https://clerk.com/privacy",
                  },
                  variables: {
                    colorPrimary: "#111827", // Gris foncé/noir Tailwind
                    colorText: isDarkMode ? "#f3f4f6" : "#1f2937",
                    colorTextSecondary: isDarkMode ? "#d1d5db" : "#4b5563",
                    colorBackground: isDarkMode ? "#1f2937" : "#ffffff",
                    colorInputBackground: isDarkMode ? "#374151" : "#f3f4f6",
                    colorInputText: isDarkMode ? "#f3f4f6" : "#1f2937",
                    fontFamily: "inherit",
                    borderRadius: "0.375rem",
                  },
                  baseTheme: isDarkMode ? dark : undefined
                }}
              />
            </div>
          </div>
        )}

        {activeTab === "app" && (
          <div className="card">
            <div className="card-header">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Paramètres de l'application</h2>
            </div>
            <div className="card-content space-y-8">
              {/* Préférences */}
              <section>
                <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">Préférences d'affichage</h3>

                {/* Préférence d'interface */}
                <InterfacePreference />

                {/* Préférence de thème */}
                <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 space-y-4 mt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Thème sombre</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Activer/désactiver le thème sombre pour l'interface
                      </p>
                    </div>
                    <div className="toggle-container">
                      <input
                        type="checkbox"
                        id="toggle-theme"
                        className="toggle-input"
                        checked={isDarkMode}
                        onChange={toggleTheme}
                      />
                      <label
                        htmlFor="toggle-theme"
                        className="toggle-bg"
                      ></label>
                    </div>
                  </div>
                </div>
              </section>

              {/* Facturation (placeholder) */}
              <section>
                <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">Facturation</h3>
                <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-full p-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-900 dark:text-white font-medium">Fonctionnalité à venir</p>
                      <p className="text-gray-500 dark:text-gray-400">
                        La gestion de la facturation sera bientôt disponible.
                      </p>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
