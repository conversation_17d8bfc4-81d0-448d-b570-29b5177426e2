import React from "react";
import { ModelSelectorProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant ModelSelector pour l'interface Comfy
 * Affiche la liste des modèles disponibles pour la catégorie sélectionnée
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  setSelectedModel,
  openCategory
}) => {
  // Filtrer les modèles par catégorie
  const filteredModels = models.filter(model => {
    // CORRECTION : Accepter les modèles même avec enabled: false pour les tests
    // if (model.enabled === false) return false;



    switch (openCategory) {
      case "chat":
        return model.chat === true;
      case "web_search":
        return model.webSearch === true;
      case "reasoning":
        return model.reasoning === true;
      default:
        return false;
    }
  });



  if (filteredModels.length === 0) {
    return (
      <div className="bg-claude-gray rounded-lg p-4 text-center">
        <p className="text-gray-400 text-sm">Aucun modèle disponible pour cette catégorie</p>
      </div>
    );
  }

  return (
    <div className="bg-claude-gray/50 rounded-xl p-3 border border-claude-light-gray/30 backdrop-blur-sm">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {filteredModels.map((model) => (
          <button
            key={model.modelId}
            onClick={() => setSelectedModel(model.modelId)}
            className={`group relative p-4 rounded-xl border transition-all duration-300 text-left overflow-hidden ${
              selectedModel === model.modelId
                ? "bg-gradient-to-br from-claude-orange to-claude-orange/80 border-claude-orange text-white shadow-lg shadow-claude-orange/25 scale-105"
                : "bg-claude-light-gray/60 border-claude-light-gray/40 text-white hover:bg-claude-light-gray/80 hover:border-claude-light-gray/60 hover:scale-102 hover:shadow-md"
            }`}
          >
            {/* Effet de brillance au survol */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-3 mb-2">
                <div className={`p-1.5 rounded-lg transition-colors ${
                  selectedModel === model.modelId
                    ? "bg-claude-light-gray/80"
                    : "bg-claude-light-gray/40 group-hover:bg-claude-light-gray/60"
                }`}>
                  <ModelIcon
                    model={model.modelId}
                    provider={model.provider}
                    size={18}
                    className="flex-shrink-0"
                  />
                </div>
                <span className="font-semibold text-sm truncate">{model.name}</span>
              </div>


            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModelSelector;
