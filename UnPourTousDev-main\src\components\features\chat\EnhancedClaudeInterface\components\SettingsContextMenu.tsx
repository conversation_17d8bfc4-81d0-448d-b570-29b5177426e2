import React, { useEffect, useState } from 'react';

interface SettingsContextMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onSettings: () => void;
  onSignOut: () => void;
}

/**
 * Menu contextuel pour le bouton Paramètres dans l'interface Claude
 */
const SettingsContextMenu: React.FC<SettingsContextMenuProps> = ({
  isOpen,
  onClose,
  onSettings,
  onSignOut
}) => {
  // État pour stocker la position et l'orientation du menu
  const [menuPosition, setMenuPosition] = useState({
    top: null as number | null,
    bottom: null as number | null,
    left: 0
  });

  // Mettre à jour la position du menu lorsqu'il s'ouvre
  useEffect(() => {
    if (isOpen) {
      // Positionner le menu par rapport au bouton Paramètres
      const settingsButton = document.querySelector('.mt-auto button') as HTMLElement;
      if (settingsButton) {
        const rect = settingsButton.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const windowWidth = window.innerWidth;
        const menuHeight = 120; // Hauteur approximative du menu
        const menuWidth = 180; // Largeur du menu (min-w-[180px])
        const margin = 10; // Marge de sécurité

        console.log('🔥 SettingsContextMenu positioning debug:', {
          buttonRect: { left: rect.left, right: rect.right, width: rect.width },
          windowWidth,
          windowHeight,
          sidebarCollapsed: rect.right < 100
        });

        // Détecter si la sidebar est collapsed
        const sidebarCollapsed = rect.right < 100;

        // Calculer la position X
        let leftPosition;
        if (sidebarCollapsed) {
          // Sidebar collapsed : positionner à droite du bouton
          leftPosition = rect.right + margin;
          console.log('🔥 Sidebar collapsed - positioning menu to RIGHT:', leftPosition);
        } else {
          // Sidebar extended : vérifier l'espace disponible
          const spaceRight = windowWidth - rect.right;
          if (spaceRight >= menuWidth + margin * 2) {
            leftPosition = rect.right + margin;
            console.log('Sidebar extended - positioning menu to RIGHT:', leftPosition);
          } else {
            leftPosition = rect.left - menuWidth - margin;
            console.log('Sidebar extended - positioning menu to LEFT:', leftPosition);
          }
        }

        // Vérifier si le menu dépasse vers le haut de l'écran
        const topPosition = rect.top - menuHeight - margin;
        const exceedsTopEdge = topPosition < 0;

        if (exceedsTopEdge) {
          // Positionner le menu en dessous du bouton
          setMenuPosition({
            top: rect.bottom + window.scrollY + margin,
            bottom: null,
            left: leftPosition
          });
        } else {
          // Positionner le menu au-dessus du bouton (comportement par défaut)
          setMenuPosition({
            top: null,
            bottom: windowHeight - rect.top + margin,
            left: leftPosition
          });
        }

        console.log('🎯 Final SettingsContextMenu position:', { left: leftPosition });
      }
    }
  }, [isOpen]);

  // Gérer le clic en dehors du menu pour le fermer
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const settingsButton = document.querySelector('.mt-auto button') as HTMLElement;
      if (isOpen && settingsButton && !settingsButton.contains(event.target as Node)) {
        const menuElement = document.getElementById('settings-menu');
        if (menuElement && !menuElement.contains(event.target as Node)) {
          onClose();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      id="settings-menu"
      className="fixed z-[100]"
      style={{
        ...(menuPosition.top !== null ? { top: menuPosition.top } : {}),
        ...(menuPosition.bottom !== null ? { bottom: menuPosition.bottom } : {}),
        left: menuPosition.left,
        pointerEvents: 'auto',
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="min-w-[180px] bg-claude-gray rounded-md p-1.5 shadow-md animate-in fade-in-80">
        {/* Option Paramètres */}
        <button
          className="flex items-center w-full px-3 py-2.5 text-sm text-gray-300 hover:bg-claude-light-gray/30 rounded-md cursor-pointer outline-none transition-colors text-left"
          onClick={(e) => {
            e.stopPropagation();
            onSettings();
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Paramètres
        </button>

        {/* Séparateur */}
        <div className="h-px bg-claude-light-gray/20 my-1.5 mx-1" />

        {/* Option Déconnexion */}
        <button
          className="flex items-center w-full px-3 py-2.5 text-sm text-red-400 hover:bg-claude-light-gray/30 rounded-md cursor-pointer outline-none transition-colors text-left"
          onClick={(e) => {
            e.stopPropagation();
            onSignOut();
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          Déconnexion
        </button>
      </div>
    </div>
  );
};

export default SettingsContextMenu;
