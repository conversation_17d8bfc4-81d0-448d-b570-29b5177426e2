import React from "react";
import { SidebarProps, Conversation } from "../types";
import { Id } from "../../../../../../convex/_generated/dataModel";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import logo from "../../../../../assets/logo2.svg";

/**
 * Composant Sidebar pour l'interface Claude
 */
const Sidebar: React.FC<SidebarProps> = ({
  activeButton,
  handleSidebarClick,
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation
}) => {
  // Formater la date de création d'une conversation
  const formatDate = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), {
      addSuffix: true,
      locale: fr
    });
  };

  return (
    <div className="w-16 border-r border-claude-light-gray flex flex-col items-center py-4 hidden sm:flex">
      <div className="flex flex-col items-center flex-grow">
        <button
          className="w-8 h-8 mb-6 flex items-center justify-center hover:bg-claude-gray/50 rounded-full transition-colors"
          title="Menu"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>

        <button
          className={`w-8 h-8 ${activeButton === "new" ? "bg-claude-orange" : "bg-claude-gray hover:bg-claude-light-gray/70"} text-white rounded-full flex items-center justify-center mb-4 relative transition-colors`}
          onClick={() => {
            handleSidebarClick("new");
            onNewConversation();
          }}
          title="Nouvelle conversation"
        >
          <img src={logo} alt="UnPourTous Logo" className="w-5 h-5 absolute" />
        </button>

        <button
          className={`w-8 h-8 ${activeButton === "chat" ? "bg-claude-orange" : "bg-claude-gray hover:bg-claude-light-gray/70"} text-white rounded-full flex items-center justify-center mb-4 transition-colors`}
          onClick={() => handleSidebarClick("chat")}
          title="Conversations"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
          </svg>
        </button>

        {/* Liste des conversations récentes (visible uniquement en mode "chat") */}
        {activeButton === "chat" && conversations.length > 0 && (
          <div className="w-full px-2 mt-2 overflow-y-auto max-h-[calc(100vh-200px)]">
            {conversations.map((conversation: Conversation) => (
              <button
                key={conversation._id}
                className={`w-full p-2 mb-2 rounded-md text-left text-xs flex items-center justify-center ${
                  currentConversationId === conversation._id
                    ? "bg-claude-light-gray/30 text-white"
                    : "text-gray-400 hover:bg-claude-light-gray/20"
                }`}
                onClick={() => onSelectConversation(conversation._id)}
                title={conversation.title}
              >
                <div className="w-8 h-8 bg-claude-gray rounded-full flex items-center justify-center">
                  {conversation.usesAutoRouter ? (
                    <img src={logo} alt="AutoRouter" className="w-4 h-4" />
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
      
      <div className="mt-auto">
        <button
          className={`w-8 h-8 ${activeButton === "settings" ? "bg-claude-orange" : "bg-claude-gray hover:bg-claude-light-gray/70"} text-white rounded-full flex items-center justify-center transition-colors`}
          onClick={() => handleSidebarClick("settings")}
          title="Paramètres"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
            <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
