"use client";

import { useState, useRef, ReactNode } from "react";
import { motion } from "framer-motion";

interface SpotlightProps {
  children: ReactNode;
  className?: string;
}

export default function SpotlightBox({ children, className = "" }: SpotlightProps) {
  return (
    <div className="flex min-h-[50vh] items-center justify-center">
      <Demo className={className}>
        {children}
      </Demo>
    </div>
  );
}

interface DemoProps {
  children: ReactNode;
  className?: string;
}

function Demo({ children, className = "" }: DemoProps) {
  const divRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!divRef.current) return;

    const div = divRef.current;
    const rect = div.getBoundingClientRect();

    setPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleFocus = () => {
    setIsFocused(true);
    setOpacity(1);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setOpacity(0);
  };

  const handleMouseEnter = () => {
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };

  return (
    <div
      ref={divRef}
      onMouseMove={handleMouseMove}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`relative max-w-2xl w-full overflow-hidden rounded-xl border border-tango-300 dark:border-bronze-600 bg-white dark:bg-bronze-800 px-4 py-4 shadow-xl transition-colors duration-200 ${className}`}
    >
      <motion.div
        className="pointer-events-none absolute -inset-px rounded-xl opacity-0"
        style={{
          background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(227, 115, 20, 0.3), transparent 40%)`,
          opacity
        }}
      />
      {children}
    </div>
  );
}
