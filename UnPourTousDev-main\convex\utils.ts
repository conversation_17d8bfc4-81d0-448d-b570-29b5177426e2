// Fonction pour déterminer si un modèle a des capacités de recherche web
export function isModelWithWebSearch(modelId: string, supportedParameters?: string[], pricingWebSearch?: string): boolean {
  // Vérifie d'abord si le modèle a un prix pour la recherche web
  if (pricingWebSearch && parseFloat(pricingWebSearch) > 0) {
    return true;
  }

  // Vérifie ensuite si le modèle supporte le paramètre web_search_options
  if (supportedParameters && supportedParameters.includes("web_search_options")) {
    return true;
  }

  // Vérifie si le modèle supporte le plugin web
  if (supportedParameters && supportedParameters.includes("plugins")) {
    return true;
  }

  // Vérifie si le modèle a le suffixe :online qui indique la capacité de recherche web
  if (modelId.endsWith(":online")) {
    return true;
  }

  // Liste des modèles connus pour avoir des capacités de recherche web
  const webSearchModels = [
    // Perplexity
    "perplexity/sonar",
    "perplexity/llama-3-sonar-small-32k-online",
    "perplexity/llama-3-sonar-large-32k-online",
    "perplexity/sonar-small-online",
    "perplexity/sonar-medium-online",
    "perplexity/sonar-large-online",
    // Anthropic
    "anthropic/claude-3-sonnet-20240229-v1:0",
    "anthropic/claude-3-opus-20240229-v1:0",
    "anthropic/claude-3-haiku-20240307-v1:0",
    "anthropic/claude-3.5-sonnet",
    // Google
    "google/gemini-pro",
    "google/gemini-1.5-pro-latest",
    "google/gemini-1.5-flash-latest",
    // Fireworks
    "fireworks/firefunction-v2",
    "fireworks/firefunction-v1",
    // Groq
    "groq/llama-3-70b-8192-online",
    // OpenAI
    "openai/gpt-4-turbo",
    "openai/gpt-4o",
    "openai/gpt-4-vision-preview",
    "openai/gpt-4-1106-preview",
    "openai/gpt-4-0125-preview"
  ];

  return webSearchModels.includes(modelId);
}

// Fonction pour déterminer si un modèle prend en charge les sorties structurées (JSON)
export function isModelWithStructuredOutput(modelId: string): boolean {
  // Liste des modèles connus pour prendre en charge les sorties structurées
  const structuredOutputModels = [
    // OpenAI
    "openai/gpt-4-turbo",
    "openai/gpt-4-turbo-preview",
    "openai/gpt-4-0125-preview",
    "openai/gpt-4-1106-preview",
    "openai/gpt-4-vision-preview",
    "openai/gpt-4",
    "openai/gpt-4-32k",
    "openai/gpt-3.5-turbo",
    "openai/gpt-3.5-turbo-16k",
    "openai/gpt-3.5-turbo-1106",
    "openai/gpt-3.5-turbo-0125",
    // Anthropic
    "anthropic/claude-3-opus-20240229-v1:0",
    "anthropic/claude-3-sonnet-20240229-v1:0",
    "anthropic/claude-3-haiku-20240307-v1:0",
    "anthropic/claude-2.1",
    "anthropic/claude-2.0",
    "anthropic/claude-instant-1.2",
    // Mistral
    "mistral/mistral-large-latest",
    "mistral/mistral-medium-latest",
    "mistral/mistral-small-latest",
    // Cohere
    "cohere/command-r",
    "cohere/command-r-plus",
    // Groq
    "groq/llama-3-8b-8192",
    "groq/llama-3-70b-8192",
    "groq/mixtral-8x7b-32768",
    "groq/gemma-7b-it",
    // Fireworks
    "fireworks/firefunction-v1",
    "fireworks/firefunction-v2",
    // Perplexity
    "perplexity/sonar-small-online",
    "perplexity/sonar-medium-online",
    "perplexity/sonar-large-online"
  ];

  return structuredOutputModels.includes(modelId);
}

// Fonction pour déterminer si un modèle a des capacités de raisonnement avancées
export function isModelWithReasoning(modelId: string): boolean {
  // Liste des modèles connus pour avoir des capacités de raisonnement avancées
  const reasoningModels = [
    // Modèles spécifiquement conçus pour le raisonnement
    "perplexity/sonar-reasoning",
    "perplexity/sonar-reasoning-pro",
    "perplexity/sonar-deep-research",
    // Modèles de grande taille avec de bonnes capacités de raisonnement
    "anthropic/claude-3-opus-20240229-v1:0",
    "anthropic/claude-3-sonnet-20240229-v1:0",
    "openai/gpt-4-turbo",
    "openai/gpt-4",
    "openai/gpt-4-32k",
    "openai/gpt-4-0125-preview",
    "openai/gpt-4-1106-preview",
    "google/gemini-1.5-pro-latest",
    "google/gemini-pro",
    "mistral/mistral-large-latest",
    "groq/llama-3-70b-8192",
    "meta-llama/llama-3-70b-instruct",
    "meta-llama/llama-3-70b-chat",
    "deepseek/deepseek-coder",
    "deepseek/deepseek-math"
  ];

  return reasoningModels.includes(modelId);
}

// Import de la configuration
import { TOKEN_LIMITS, STREAMING_SUPPORTED_MODELS } from "./config";

// Fonction pour déterminer la limite de tokens appropriée en fonction du modèle

export function getModelMaxTokens(modelId: string): number {
  // Vérifie d'abord si le modèle a une limite spécifique définie

  // Si c'est un modèle de raisonnement, utilise la limite de raisonnement
  if (isModelWithReasoning(modelId)) {
    // Vérifie si le modèle a une limite spécifique dans la catégorie REASONING
    if (modelId in TOKEN_LIMITS.REASONING) {
      return TOKEN_LIMITS.REASONING[modelId as keyof typeof TOKEN_LIMITS.REASONING];
    }
    // Sinon, utilise la valeur par défaut pour les modèles de raisonnement
    return TOKEN_LIMITS.REASONING.DEFAULT;
  }

  // Si c'est un modèle avec recherche web, utilise la limite de recherche web
  if (isModelWithWebSearch(modelId)) {
    // Vérifie si le modèle a une limite spécifique dans la catégorie WEB_SEARCH
    if (modelId in TOKEN_LIMITS.WEB_SEARCH) {
      return TOKEN_LIMITS.WEB_SEARCH[modelId as keyof typeof TOKEN_LIMITS.WEB_SEARCH];
    }
    // Sinon, utilise la valeur par défaut pour les modèles de recherche web
    return TOKEN_LIMITS.WEB_SEARCH.DEFAULT;
  }

  // Pour les modèles de chat standard
  // Vérifie si le modèle a une limite spécifique dans la catégorie CHAT
  if (modelId in TOKEN_LIMITS.CHAT) {
    return TOKEN_LIMITS.CHAT[modelId as keyof typeof TOKEN_LIMITS.CHAT];
  }

  // Si aucune catégorie spécifique n'est trouvée, utilise la valeur par défaut pour les modèles de chat
  return TOKEN_LIMITS.CHAT.DEFAULT;
}

// Fonction pour déterminer si un modèle prend en charge le streaming
export function isModelWithStreaming(modelId: string): boolean {
  return STREAMING_SUPPORTED_MODELS.includes(modelId);
}
