import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

export const list = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    // Récupère toutes les conversations de l'utilisateur
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .order("desc")
      .collect();

    // Pour chaque conversation, récupère le dernier message de l'assistant
    const conversationsWithLastModel = await Promise.all(
      conversations.map(async (conversation) => {
        // Récupère le dernier message de l'assistant pour cette conversation
        const lastAssistantMessage = await ctx.db
          .query("messages")
          .withIndex("by_conversation", (q) => q.eq("conversationId", conversation._id))
          .filter((q) => q.eq(q.field("role"), "assistant"))
          .order("desc")
          .first();

        // Si un message d'assistant existe, récupère le modèle utilisé
        if (lastAssistantMessage) {
          // Extrait le fournisseur du modèle (ex: "openai/gpt-4" -> "openai")
          const modelUsed = lastAssistantMessage.modelUsed;
          const provider = modelUsed.split('/')[0] || "default";

          return {
            ...conversation,
            lastModelUsed: modelUsed,
            lastModelProvider: provider
          };
        }

        return conversation;
      })
    );

    return conversationsWithLastModel;
  },
});

// Liste les conversations dans un dossier spécifique
export const listByFolder = query({
  args: {
    folderId: v.optional(v.id("folders")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    let conversations;

    if (args.folderId) {
      // Conversations dans un dossier spécifique
      conversations = await ctx.db
        .query("conversations")
        .withIndex("by_folder", (q) => q.eq("folderId", args.folderId))
        .filter((q) => q.eq(q.field("userId"), identity.subject))
        .order("desc")
        .collect();
    } else {
      // Conversations sans dossier (racine)
      conversations = await ctx.db
        .query("conversations")
        .withIndex("by_user", (q) => q.eq("userId", identity.subject))
        .filter((q) => q.eq(q.field("folderId"), undefined))
        .order("desc")
        .collect();
    }

    // Pour chaque conversation, récupère le dernier message de l'assistant
    const conversationsWithLastModel = await Promise.all(
      conversations.map(async (conversation) => {
        // Récupère le dernier message de l'assistant pour cette conversation
        const lastAssistantMessage = await ctx.db
          .query("messages")
          .withIndex("by_conversation", (q) => q.eq("conversationId", conversation._id))
          .filter((q) => q.eq(q.field("role"), "assistant"))
          .order("desc")
          .first();

        // Si un message d'assistant existe, récupère le modèle utilisé
        if (lastAssistantMessage) {
          // Extrait le fournisseur du modèle (ex: "openai/gpt-4" -> "openai")
          const modelUsed = lastAssistantMessage.modelUsed;
          const provider = modelUsed.split('/')[0] || "default";

          return {
            ...conversation,
            lastModelUsed: modelUsed,
            lastModelProvider: provider
          };
        }

        return conversation;
      })
    );

    return conversationsWithLastModel;
  },
});

export const rename = mutation({
  args: {
    id: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de la conversation
    const conversation = await ctx.db.get(args.id);
    if (!conversation) throw new Error("Conversation non trouvée");
    if (conversation.userId !== identity.subject) throw new Error("Non autorisé");

    // Renomme la conversation
    await ctx.db.patch(args.id, { title: args.title });
    return { success: true };
  },
});

export const remove = mutation({
  args: {
    id: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de la conversation
    const conversation = await ctx.db.get(args.id);
    if (!conversation) throw new Error("Conversation non trouvée");
    if (conversation.userId !== identity.subject) throw new Error("Non autorisé");

    // Supprime la conversation
    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Déplace une conversation dans un dossier
export const moveToFolder = mutation({
  args: {
    id: v.id("conversations"),
    folderId: v.optional(v.id("folders")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Vérifie que l'utilisateur est propriétaire de la conversation
    const conversation = await ctx.db.get(args.id);
    if (!conversation) throw new Error("Conversation non trouvée");
    if (conversation.userId !== identity.subject) throw new Error("Non autorisé");

    // Si un folderId est fourni, vérifie que l'utilisateur est propriétaire du dossier
    if (args.folderId) {
      const folder = await ctx.db.get(args.folderId);
      if (!folder) throw new Error("Dossier non trouvé");
      if (folder.userId !== identity.subject) throw new Error("Non autorisé");
    }

    // Déplace la conversation
    await ctx.db.patch(args.id, { folderId: args.folderId });
    return { success: true };
  },
});

// Fonction pour créer une nouvelle conversation
export const create = mutation({
  args: {
    title: v.string(),
    modelId: v.optional(v.string()),
    usesAutoRouter: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Crée une nouvelle conversation
    const conversationId = await ctx.db.insert("conversations", {
      userId: identity.subject,
      title: args.title,
      modelId: args.modelId,
      usesAutoRouter: args.usesAutoRouter || false,
    });

    return conversationId;
  },
});

// Fonction pour récupérer une conversation par son ID
export const getById = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== identity.subject) {
      return null;
    }

    return conversation;
  },
});

