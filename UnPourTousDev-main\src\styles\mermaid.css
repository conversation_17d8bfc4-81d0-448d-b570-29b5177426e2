/* Styles pour masquer les messages d'erreur de Mermaid */

/* Masquer tous les messages d'erreur générés par Mermaid */
.error-icon, .error-text, .error-message, .error, .error-line, .error-span {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  pointer-events: none !important;
}

/* Isolation complète des conteneurs Mermaid */
.safe-mermaid-container {
  position: relative;
  max-height: 500px;
  overflow: hidden;
  isolation: isolate; /* Crée un nouveau contexte d'empilement */
  contain: content; /* Contient tout le contenu à l'intérieur */
}

/* Limiter la hauteur des diagrammes Mermaid */
.safe-mermaid-container svg {
  max-height: 500px !important;
  max-width: 100% !important;
  overflow: hidden !important;
  display: block !important;
}

/* Masquer tous les éléments d'erreur spécifiques */
g[class*="error"],
g[id*="error"],
[class*="error"],
[id*="error"],
g[class*="Error"],
g[id*="Error"],
[class*="Error"],
[id*="Error"],
text:has(tspan:contains("error")),
text:has(tspan:contains("Error")) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Styles pour les diagrammes Mermaid */
.mermaid-diagram {
  position: relative;
  border-radius: 0.375rem;
  background-color: rgba(245, 245, 245, 0.1);
  max-height: 500px;
  overflow: hidden;
}

.dark .mermaid-diagram {
  background-color: rgba(30, 30, 30, 0.3);
}

/* Empêcher les éléments de déborder */
svg {
  max-width: 100% !important;
  max-height: 500px !important;
}

/* Masquer les éléments qui apparaissent en bas de page */
body > div:last-child:not(.app):not(#root) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}
