import React from "react";
import { ActionButtonsProps } from "../types";
import SendButton from "../../../../common/SendButton";

/**
 * Composant ActionButtons pour l'interface Comfy
 * Contient uniquement le bouton d'envoi avec transition Envoyer → Stop
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  userMessage,
  isLoading,
  startChat,
  onStop
}) => {
  return (
    <div className="flex items-center">
      {/* Bouton d'envoi avec transition automatique Envoyer → Stop */}
      <SendButton
        onClick={() => startChat()}
        disabled={!userMessage.trim()}
        size="sm"
        isLoading={isLoading}
        onStop={onStop}
      />
    </div>
  );
};

export default ActionButtons;
