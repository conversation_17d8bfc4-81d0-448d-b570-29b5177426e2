/**
 * Constantes de l'application
 */

// <PERSON><PERSON><PERSON> d'attente pour les requêtes API (en ms)
export const API_TIMEOUT = 30000;

// Nombre maximum de messages à afficher dans une conversation
export const MAX_MESSAGES_DISPLAYED = 100;

// Nombre maximum de caractères pour le titre d'une conversation
export const MAX_CONVERSATION_TITLE_LENGTH = 50;

// Nombre maximum de caractères pour le nom d'un dossier
export const MAX_FOLDER_NAME_LENGTH = 30;

// Délai de debounce pour la recherche (en ms)
export const SEARCH_DEBOUNCE_DELAY = 300;

// Délai d'inactivité avant de considérer l'utilisateur comme inactif (en ms)
export const USER_INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes
