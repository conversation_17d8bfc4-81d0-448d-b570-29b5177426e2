import React from "react";
import { AiMessageProps } from "./types";
import { useAiMessage } from "./hooks/useAiMessage";
import MessageHeader from "./components/MessageHeader";
import MessageContent from "./components/MessageContent";
import ReasoningSection from "./components/ReasoningSection";
import CopyButton from "../../../common/CopyButton";

/**
 * Composant AiMessage principal
 */
const AiMessage: React.FC<AiMessageProps> = ({ message }) => {
  // Utiliser le hook personnalisé pour gérer l'état et la logique
  const {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter,
    isReasoningModel
  } = useAiMessage(message);

  return (
    <div className="bg-claude-light-gray/45 rounded-xl p-4 text-white relative group">

        {/* En-tête du message */}
        <MessageHeader
          modelName={modelName}
          modelUsed={message.modelUsed}
          isWebSearchModel={isWebSearchModel}
          isAutoRouter={isAutoRouter}
          isStreaming={message.isStreaming}
        />

        {/* Section de raisonnement pour les modèles compatibles - AVANT le message */}
        {(message.reasoning_content || (message.isStreaming && isReasoningModel)) && (
          <ReasoningSection
            reasoningContent={message.reasoning_content || ""}
            isStreaming={message.isStreaming}
            isReasoningModel={isReasoningModel}
          />
        )}

        {/* Contenu du message */}
        <MessageContent
          content={message.content}
          references={message.references}
          isStreaming={message.isStreaming}
          showCursor={showCursor}
        />

        {/* Copy button for entire message - positioned at bottom right */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
          <CopyButton
            text={message.content}
            variant="message"
            size="md"
          />
        </div>
    </div>
  );
};

export default AiMessage;
