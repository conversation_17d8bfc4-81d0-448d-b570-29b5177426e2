import React from "react";
import { AiMessageProps } from "./types";
import { useAiMessage } from "./hooks/useAiMessage";
import MessageHeader from "./components/MessageHeader";
import MessageContent from "./components/MessageContent";
import ReasoningSection from "./components/ReasoningSection";

/**
 * Composant AiMessage principal
 */
const AiMessage: React.FC<AiMessageProps> = ({ message }) => {
  // Utiliser le hook personnalisé pour gérer l'état et la logique
  const {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter,
    isReasoningModel
  } = useAiMessage(message);

  return (
    <div className="flex mb-4 w-full">
      <div className="bg-claude-light-gray/45 rounded-xl p-4 max-w-[95%] text-white">
        {/* En-tête du message */}
        <MessageHeader
          modelName={modelName}
          modelUsed={message.modelUsed}
          isWebSearchModel={isWebSearchModel}
          isAutoRouter={isAutoRouter}
          isStreaming={message.isStreaming}
        />

        {/* Section de raisonnement pour les modèles compatibles - AVANT le message */}
        {(message.reasoning_content || (message.isStreaming && isReasoningModel)) && (
          <ReasoningSection
            reasoningContent={message.reasoning_content || ""}
            isStreaming={message.isStreaming}
            isReasoningModel={isReasoningModel}
          />
        )}

        {/* Contenu du message */}
        <MessageContent
          content={message.content}
          references={message.references}
          isStreaming={message.isStreaming}
          showCursor={showCursor}
        />
      </div>
    </div>
  );
};

export default AiMessage;
