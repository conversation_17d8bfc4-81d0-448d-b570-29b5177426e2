import katex from 'katex';

/**
 * Prétraite le contenu Markdown pour convertir les formules LaTeX en HTML
 * Cette fonction peut être utilisée comme alternative au rendu via rehype-katex
 */
export function processLatexInContent(content: string): string {
  // Traiter les formules en bloc ($$...$$)
  content = content.replace(/\$\$(.*?)\$\$/gs, (match, formula) => {
    try {
      return `<div class="katex-display">${katex.renderToString(formula, {
        displayMode: true,
        throwOnError: false,
        strict: 'ignore'
      })}</div>`;
    } catch (error) {
      console.error('KaTeX error (block):', error);
      return `<div class="katex-error">Erreur de rendu: ${formula}</div>`;
    }
  });

  // Traiter les formules en ligne ($...$)
  content = content.replace(/\$([^\$]+?)\$/g, (match, formula) => {
    try {
      return `<span class="katex-inline">${katex.renderToString(formula, {
        displayMode: false,
        throwOnError: false,
        strict: 'ignore'
      })}</span>`;
    } catch (error) {
      console.error('KaTeX error (inline):', error);
      return `<span class="katex-error">Erreur de rendu: ${formula}</span>`;
    }
  });

  // Traiter les formules en bloc (\[...\])
  content = content.replace(/\\\[(.*?)\\\]/gs, (match, formula) => {
    try {
      return `<div class="katex-display">${katex.renderToString(formula, {
        displayMode: true,
        throwOnError: false,
        strict: 'ignore'
      })}</div>`;
    } catch (error) {
      console.error('KaTeX error (block \\[):', error);
      return `<div class="katex-error">Erreur de rendu: ${formula}</div>`;
    }
  });

  // Traiter les formules en ligne (\(...\))
  content = content.replace(/\\\((.*?)\\\)/g, (match, formula) => {
    try {
      return `<span class="katex-inline">${katex.renderToString(formula, {
        displayMode: false,
        throwOnError: false,
        strict: 'ignore'
      })}</span>`;
    } catch (error) {
      console.error('KaTeX error (inline \\():', error);
      return `<span class="katex-error">Erreur de rendu: ${formula}</span>`;
    }
  });

  return content;
}
