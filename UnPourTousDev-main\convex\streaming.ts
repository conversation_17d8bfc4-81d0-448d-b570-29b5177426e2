import { action, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { extractWebReferenceFromAnnotations } from "./openrouter";

// Action pour gérer le streaming des messages
export const streamMessage = action({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
    modelName: v.optional(v.string()),
    messages: v.array(
      v.object({
        role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
        content: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    try {
      console.log("🚀 [Streaming] Début du streaming pour:", args.modelId);

      // Créer un message temporaire avec isStreaming = true
      const messageId = await ctx.runMutation(internal.streaming.createStreamingMessage, {
        conversationId: args.conversationId,
        modelId: args.modelId,
        modelName: args.modelName,
      });

      // Vérifier si le modèle supporte le streaming
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error("Clé API OpenRouter non configurée");
      }

      // Appeler l'API OpenRouter avec streaming activé
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
          "HTTP-Referer": "https://unpourtouschat.com",
          "X-Title": "UnPourTous Chat"
        },
        body: JSON.stringify({
          model: args.modelId,
          messages: args.messages,
          stream: true,
          max_tokens: 4000,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter Error: ${response.status} ${response.statusText}`);
      }

      // Traiter le stream
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Impossible de lire le stream de réponse");
      }

      let fullContent = "";
      let reasoningContent = "";
      let buffer = ""; // Buffer pour accumuler les chunks partiels
      let finalResponse: any = null; // Pour stocker la réponse finale avec les métadonnées
      let selectedModel: string | null = null; // Pour stocker le modèle sélectionné par AutoRouter
      let selectedModelName: string | null = null; // Pour stocker le nom du modèle sélectionné
      const decoder = new TextDecoder();

      // Batching variables for optimized updates
      let lastUpdateTime = Date.now();
      let pendingUpdate = false;
      const UPDATE_INTERVAL = 500; // Update every 500ms
      const MIN_CONTENT_CHANGE = 50; // Minimum characters before update

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          buffer += chunk; // Ajouter au buffer
          const lines = buffer.split('\n');

          // Garder la dernière ligne incomplète dans le buffer
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices?.[0]?.delta;

                // Stocker la réponse finale pour extraire les métadonnées
                if (parsed.choices?.[0]?.finish_reason ||
                    parsed.choices?.[0]?.message ||
                    parsed.model) {
                  finalResponse = parsed;
                }

                if (delta?.content) {
                  fullContent += delta.content;

                  // Batched updates: only update if enough time has passed or significant content change
                  const now = Date.now();
                  const timeSinceLastUpdate = now - lastUpdateTime;
                  const shouldUpdate = timeSinceLastUpdate >= UPDATE_INTERVAL ||
                                     (fullContent.length % MIN_CONTENT_CHANGE === 0);

                  if (shouldUpdate && !pendingUpdate) {
                    pendingUpdate = true;
                    lastUpdateTime = now;

                    // Update the message with batched content
                    await ctx.runMutation(internal.streaming.updateStreamingMessage, {
                      messageId,
                      content: fullContent,
                      isStreaming: true,
                    });

                    pendingUpdate = false;
                  }
                }

                // Capturer le raisonnement si disponible (pour les modèles de raisonnement)
                if (delta?.reasoning) {
                  reasoningContent += delta.reasoning;

                  await ctx.runMutation(internal.streaming.updateStreamingMessage, {
                    messageId,
                    content: fullContent,
                    reasoning_content: reasoningContent,
                    isStreaming: true,
                  });
                }

                // Capturer les annotations delta immédiatement
                if (delta?.annotations) {
                  const deltaReferences = extractWebReferenceFromAnnotations(delta.annotations);
                  if (deltaReferences.length > 0) {
                    // Stocker les références pour la finalisation
                    finalResponse = { ...finalResponse, deltaReferences };
                  }
                }

                // Capturer le modèle sélectionné par AutoRouter
                if (args.modelId === "openrouter/auto" && parsed.model && !selectedModel) {
                  selectedModel = parsed.model;
                  console.log("🎯 [Streaming] AutoRouter a sélectionné le modèle:", selectedModel);

                  // Récupérer le nom d'affichage du modèle depuis la base de données
                  try {
                    const modelInfo = await ctx.runQuery(api.livemodels.list);
                    // Recherche exacte d'abord
                    let foundModel = modelInfo.find((m: any) => m.modelId === selectedModel);

                    // Si pas trouvé, recherche par correspondance partielle (pour les variantes de modèles)
                    if (!foundModel && selectedModel) {
                      foundModel = modelInfo.find((m: any) =>
                        selectedModel.includes(m.modelId) || m.modelId.includes(selectedModel.split('/')[1] || selectedModel)
                      );
                    }

                    selectedModelName = foundModel?.name || selectedModel;

                    console.log("📝 [Streaming] Modèle technique:", selectedModel);
                    console.log("📝 [Streaming] Nom d'affichage trouvé:", selectedModelName);
                    console.log("📝 [Streaming] Modèle trouvé dans livemodels:", foundModel ? "OUI" : "NON");

                    // Mettre à jour le message avec le vrai modèle
                    await ctx.runMutation(internal.streaming.updateStreamingMessage, {
                      messageId,
                      content: fullContent,
                      reasoning_content: reasoningContent || undefined,
                      isStreaming: true,
                      modelUsed: selectedModel,
                      modelName: selectedModelName,
                    });
                  } catch (error) {
                    console.warn("⚠️ [Streaming] Erreur lors de la récupération du nom du modèle:", error);
                    selectedModelName = selectedModel;
                  }
                }
              } catch (parseError) {
                // Ignorer silencieusement les erreurs de parsing des chunks partiels
                // Ces erreurs sont normales lors du streaming et ne nécessitent pas de log
                if (parseError instanceof SyntaxError &&
                    (parseError.message.includes("Unterminated string") ||
                     parseError.message.includes("Expected") ||
                     parseError.message.includes("Unexpected end"))) {
                  // Chunk partiel, on continue sans logger
                  continue;
                } else {
                  // Autres erreurs de parsing, on les log
                  console.warn("Erreur de parsing inattendue:", parseError);
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // Final update to ensure all content is saved before adding sources
      await ctx.runMutation(internal.streaming.updateStreamingMessage, {
        messageId,
        content: fullContent,
        reasoning_content: reasoningContent || undefined,
        isStreaming: true,
      });

      // Extraire les sources si disponibles
      let references: { id: string; title: string; url: string }[] = [];

      // Extract sources from annotations (message final)
      if (finalResponse?.choices?.[0]?.message?.annotations) {
        const annotations = finalResponse.choices[0].message.annotations;
        const extractedReferences = extractWebReferenceFromAnnotations(annotations);
        if (extractedReferences.length > 0) {
          references = extractedReferences;
        }
      }

      // Extract sources from annotations (delta) - priorité aux références déjà extraites
      if (references.length === 0 && finalResponse?.deltaReferences) {
        references = finalResponse.deltaReferences;
      }

      // Fallback: extraire depuis les annotations delta si pas encore fait
      if (references.length === 0 && finalResponse?.choices?.[0]?.delta?.annotations) {
        const annotations = finalResponse.choices[0].delta.annotations;
        const extractedReferences = extractWebReferenceFromAnnotations(annotations);
        if (extractedReferences.length > 0) {
          references = extractedReferences;
        }
      }

      // Extract sources from text content as fallback
      if (references.length === 0 && fullContent) {
        const { extractReferences } = await import("./openrouter");
        const textReferences = extractReferences(fullContent);
        if (textReferences.length > 0) {
          references = textReferences;
        }
      }

      // Log final results only
      if (references.length > 0) {
        console.log("✅ [Streaming] Sources extraites:", references.length, "références");
      }

      // Finaliser le message avec les sources et le modèle sélectionné
      await ctx.runMutation(internal.streaming.finalizeStreamingMessage, {
        messageId,
        content: fullContent,
        reasoning_content: reasoningContent || undefined,
        references: references.length > 0 ? references : undefined,
        modelUsed: selectedModel || undefined,
        modelName: selectedModelName || undefined,
      });

      console.log("✅ [Streaming] Streaming terminé avec succès");
      return { success: true, messageId };

    } catch (error) {
      console.error("❌ [Streaming] Erreur:", error);
      throw error;
    }
  },
});

// Mutation interne pour créer un message en streaming
export const createStreamingMessage = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
    modelName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: "assistant",
      content: "",
      modelUsed: args.modelId,
      modelName: args.modelName,
      isStreaming: true,
    });
  },
});

// Mutation interne pour mettre à jour un message en streaming
export const updateStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
    content: v.string(),
    reasoning_content: v.optional(v.string()),
    isStreaming: v.boolean(),
    modelUsed: v.optional(v.string()),
    modelName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      content: args.content,
      reasoning_content: args.reasoning_content,
      isStreaming: args.isStreaming,
    };

    // Mettre à jour le modèle si fourni (pour AutoRouter)
    if (args.modelUsed) {
      updates.modelUsed = args.modelUsed;
    }
    if (args.modelName) {
      updates.modelName = args.modelName;
    }

    await ctx.db.patch(args.messageId, updates);
  },
});

// Mutation interne pour finaliser un message en streaming
export const finalizeStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
    content: v.string(),
    reasoning_content: v.optional(v.string()),
    references: v.optional(v.array(v.object({
      id: v.string(),
      title: v.string(),
      url: v.string(),
    }))),
    modelUsed: v.optional(v.string()),
    modelName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      content: args.content,
      reasoning_content: args.reasoning_content,
      references: args.references,
      isStreaming: false,
    };

    // Mettre à jour le modèle si fourni (pour AutoRouter)
    if (args.modelUsed) {
      updates.modelUsed = args.modelUsed;
    }
    if (args.modelName) {
      updates.modelName = args.modelName;
    }

    await ctx.db.patch(args.messageId, updates);
  },
});
