import React from "react";
import { MessageTextareaProps } from "../types";
import SendButton from "../../../../common/SendButton";

/**
 * Composant pour la zone de texte du message
 */
const MessageTextarea: React.FC<MessageTextareaProps> = ({
  message,
  setMessage,
  isSubmitting
}) => {
  return (
    <div className="relative mb-6">
      <textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (message.trim() && !isSubmitting) {
              // Créer un événement de soumission de formulaire avec un submitter
              const form = e.currentTarget.closest('form');
              if (form) {
                const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
                if (submitButton) {
                  // Simuler un clic sur le bouton d'envoi
                  submitButton.click();
                }
              }
            }
          }
        }}
        placeholder="Pose moi une question !"
        className="w-full p-3 min-h-[44px] max-h-[300px] border border-tango-300 dark:border-bronze-600 rounded-lg bg-white dark:bg-bronze-800 text-bronze-700 dark:text-tango-100 transition-colors duration-200 focus:outline-none focus:border-fire-700 focus:border-2 resize-none"
        style={{
          height: 'auto',
          overflowY: message.split('\n').length > 11 ? 'auto' : 'hidden',
          wordWrap: 'break-word',
          whiteSpace: 'pre-wrap'
        }}
        rows={1}
        onInput={(e) => {
          // Ajuster automatiquement la hauteur
          const target = e.target as HTMLTextAreaElement;
          target.style.height = 'auto';
          const newHeight = Math.min(target.scrollHeight, 300);
          target.style.height = `${newHeight}px`;

          // Afficher la barre de défilement uniquement si plus de 11 lignes
          const lineCount = target.value.split('\n').length;
          target.style.overflowY = lineCount > 11 ? 'auto' : 'hidden';
        }}
      />

      {/* Bouton d'envoi intégré dans la zone de texte */}
      <div className="absolute bottom-3 right-3">
        <SendButton
          type="submit"
          disabled={!message.trim() || isSubmitting}
          size="sm"
        >
          {isSubmitting && (
            <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
        </SendButton>
      </div>
    </div>
  );
};

export default MessageTextarea;
