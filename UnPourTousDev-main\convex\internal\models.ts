import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// Récupère tous les modèles (pour l'administration)
export const listAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("models").collect();
  },
});

// Fonction pour synchroniser les modèles depuis OpenRouter
export const syncModelsFromOpenRouter = mutation({
  args: {
    models: v.array(
      v.object({
        name: v.string(),
        modelId: v.string(),
        provider: v.string(),
        description: v.string(),
        enabled: v.boolean(),
        contextLength: v.optional(v.number()),
        pricing: v.optional(
          v.object({
            prompt: v.number(),
            completion: v.number(),
          })
        ),
      })
    ),
  },
  handler: async (ctx, args) => {
    // Récupère les modèles existants
    const existingModels = await ctx.db.query("models").collect();

    // Crée un map des modèles existants pour faciliter la recherche
    const existingModelsMap = new Map();
    existingModels.forEach(model => {
      existingModelsMap.set(model.modelId, model);
    });

    // Compteurs pour les statistiques
    let added = 0;
    let updated = 0;

    // Traite chaque modèle
    for (const model of args.models) {
      const existingModel = existingModelsMap.get(model.modelId);

      if (existingModel) {
        // Met à jour le modèle existant
        await ctx.db.patch(existingModel._id, {
          name: model.name,
          description: model.description,
          contextLength: model.contextLength,
          pricing: model.pricing,
        });
        updated++;
      } else {
        // Ajoute un nouveau modèle
        await ctx.db.insert("models", {
          ...model,
          custom: false,
        });
        added++;
      }
    }

    return { success: true, added, updated };
  },
});
