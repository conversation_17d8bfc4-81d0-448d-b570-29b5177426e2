import React from "react";
import MarkdownRenderer from "./common/MarkdownRenderer";

interface UserMessageProps {
  message: {
    content: string;
  };
}

export default function UserMessage({ message }: UserMessageProps) {
  return (
    <div className="flex justify-end mb-4 w-full">
      <div className="bg-gray-900 dark:bg-black text-white rounded-lg py-2 px-4 max-w-[95%]">
        <div className="prose prose-invert prose-sm max-w-none markdown-content w-full break-words whitespace-pre-wrap">
          <MarkdownRenderer content={message.content} />
        </div>
      </div>
    </div>
  );
}
