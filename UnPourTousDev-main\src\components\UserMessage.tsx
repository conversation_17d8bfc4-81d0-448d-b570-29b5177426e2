import React from "react";
import <PERSON><PERSON><PERSON>ender<PERSON> from "./common/MarkdownRenderer";
import CopyButton from "./common/CopyButton";

interface UserMessageProps {
  message: {
    content: string;
  };
}

export default function UserMessage({ message }: UserMessageProps) {
  return (
    <div className="bg-gray-900 dark:bg-black text-white rounded-lg py-2 px-4 relative group">
      <div className="prose prose-invert prose-sm max-w-none markdown-content w-full break-words whitespace-pre-wrap">
        <MarkdownRenderer content={message.content} />
      </div>

      {/* Copy button for entire user message - positioned at bottom right */}
      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
        <CopyButton
          text={message.content}
          variant="message"
          size="md"
        />
      </div>
    </div>
  );
}
