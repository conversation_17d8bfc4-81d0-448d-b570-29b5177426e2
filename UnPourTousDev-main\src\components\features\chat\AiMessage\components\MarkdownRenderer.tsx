import React, { useState } from "react";
import { MarkdownRendererProps } from "../types";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import CopyButton from "../../../../common/CopyButton";

/**
 * Composant pour le rendu du contenu Markdown
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  references,
  isStreaming,
  showCursor
}) => {
  // Fonction pour traiter les références dans le contenu
  const processReferences = (content: string): string => {
    if (!references || references.length === 0) {
      return content;
    }

    // Remplacer les références [n] par des liens Markdown
    let processedContent = content;
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(
        refRegex,
        `[${refNumber}](${ref.url} "${ref.title}")`
      );
    });

    return processedContent;
  };

  // Fonction pour nettoyer les sauts de ligne répétitifs et problématiques
  const cleanLineBreaks = (content: string): string => {
    return content
      // Supprimer les sauts de ligne multiples (plus de 2 consécutifs)
      .replace(/\n{3,}/g, '\n\n')
      // Supprimer les sauts de ligne après les tirets suivis d'un saut de ligne
      .replace(/(-\s*)\n+/g, '$1')
      // Supprimer les sauts de ligne après les points suivis d'un saut de ligne en début de phrase
      .replace(/(\.\s*)\n+([a-zA-Z])/g, '$1 $2')
      // Supprimer les sauts de ligne isolés après les deux-points
      .replace(/:\s*\n+(-)/g, ':\n$1')
      // Nettoyer les espaces en fin de ligne
      .replace(/[ \t]+$/gm, '')
      // Supprimer les lignes vides contenant seulement des espaces
      .replace(/^\s*$/gm, '');
  };

  // Traiter les références dans le contenu et nettoyer les sauts de ligne
  const processedContent = processReferences(cleanLineBreaks(content));

  return (
    <div className="relative">
      <div className="markdown-content prose dark:prose-invert prose-sm max-w-none break-words whitespace-pre-wrap">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]} // Support pour les tableaux, listes de tâches, etc.
          components={{
            // Personnaliser le rendu des éléments pour éviter les barres de défilement
            pre: ({ children }) => (
              <div className="whitespace-pre-wrap break-words">{children}</div>
            ),
            code: ({ className, children, inline, ...props }: any) => {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                // Blocs de code avec coloration syntaxique et bouton de copie
                <div className="code-block-container relative group">
                  {/* Copy button for code blocks */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
                    <CopyButton
                      text={String(children).replace(/\n$/, '')}
                      variant="code"
                      size="sm"
                    />
                  </div>
                  <SyntaxHighlighter
                    style={{
                      ...vscDarkPlus,
                      'pre[class*="language-"]': {
                        ...vscDarkPlus['pre[class*="language-"]'],
                        background: 'transparent',
                        backgroundColor: 'transparent',
                      },
                      'code[class*="language-"]': {
                        ...vscDarkPlus['code[class*="language-"]'],
                        background: 'transparent',
                        backgroundColor: 'transparent',
                      }
                    }}
                    language={match[1]}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      paddingRight: '2.5rem', // Make room for copy button
                      fontSize: '0.875rem',
                      lineHeight: 1.4,
                      background: 'transparent',
                      backgroundColor: 'transparent',
                      fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    }}
                    wrapLines={true}
                    wrapLongLines={true}
                    useInlineStyles={true}
                    showLineNumbers={false}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                </div>
              ) : (
                // Code en ligne
                <code className="bg-gray-700 px-1 py-0.5 rounded text-sm text-gray-200" {...props}>
                  {children}
                </code>
              );
            },
            // Personnaliser le rendu des tableaux
            table: ({ children }) => (
              <div className="overflow-x-auto my-4">
                <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-600">{children}</table>
              </div>
            ),
            th: ({ children }) => (
              <th className="px-4 py-2 bg-gray-700 text-left text-xs font-bold text-white uppercase tracking-wider border border-gray-300 dark:border-gray-600">{children}</th>
            ),
            td: ({ children }) => (
              <td className="px-4 py-2 text-sm text-white border border-gray-300 dark:border-gray-600">{children}</td>
            ),
            // Enhanced image handling with loading states and error fallbacks
            img: ({ src, alt, ...props }) => {
              const [imageLoaded, setImageLoaded] = useState(false);
              const [imageError, setImageError] = useState(false);

              return (
                <div className="my-4 relative">
                  {!imageLoaded && !imageError && (
                    <div className="flex items-center justify-center h-32 bg-gray-800 rounded-lg animate-pulse">
                      <div className="text-gray-400 text-sm">Loading image...</div>
                    </div>
                  )}
                  {imageError && (
                    <div className="flex items-center justify-center h-32 bg-gray-800 rounded-lg border-2 border-dashed border-gray-600">
                      <div className="text-center text-gray-400">
                        <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <div className="text-sm">Failed to load image</div>
                        {alt && <div className="text-xs mt-1">{alt}</div>}
                      </div>
                    </div>
                  )}
                  <img
                    src={src}
                    alt={alt}
                    onLoad={() => setImageLoaded(true)}
                    onError={() => setImageError(true)}
                    className={`
                      max-w-full h-auto rounded-lg shadow-lg
                      ${imageLoaded ? 'block' : 'hidden'}
                    `}
                    {...props}
                  />
                </div>
              );
            },
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>

      {/* Curseur clignotant */}
      {isStreaming && showCursor && (
        <span className="animate-pulse absolute bottom-0 right-0">▌</span>
      )}
    </div>
  );
};

export default MarkdownRenderer;
