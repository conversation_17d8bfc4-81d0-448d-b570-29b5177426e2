import React from "react";
import { MarkdownRendererProps } from "../types";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

/**
 * Composant pour le rendu du contenu Markdown
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  references,
  isStreaming,
  showCursor
}) => {
  // Fonction pour traiter les références dans le contenu
  const processReferences = (content: string): string => {
    if (!references || references.length === 0) {
      return content;
    }

    // Remplacer les références [n] par des liens Markdown
    let processedContent = content;
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(
        refRegex,
        `[${refNumber}](${ref.url} "${ref.title}")`
      );
    });

    return processedContent;
  };

  // Fonction pour nettoyer les sauts de ligne répétitifs et problématiques
  const cleanLineBreaks = (content: string): string => {
    return content
      // Supprimer les sauts de ligne multiples (plus de 2 consécutifs)
      .replace(/\n{3,}/g, '\n\n')
      // Supprimer les sauts de ligne après les tirets suivis d'un saut de ligne
      .replace(/(-\s*)\n+/g, '$1')
      // Supprimer les sauts de ligne après les points suivis d'un saut de ligne en début de phrase
      .replace(/(\.\s*)\n+([a-zA-Z])/g, '$1 $2')
      // Supprimer les sauts de ligne isolés après les deux-points
      .replace(/:\s*\n+(-)/g, ':\n$1')
      // Nettoyer les espaces en fin de ligne
      .replace(/[ \t]+$/gm, '')
      // Supprimer les lignes vides contenant seulement des espaces
      .replace(/^\s*$/gm, '');
  };

  // Traiter les références dans le contenu et nettoyer les sauts de ligne
  const processedContent = processReferences(cleanLineBreaks(content));

  return (
    <div className="relative">
      <div className="markdown-content prose dark:prose-invert prose-sm max-w-none break-words whitespace-pre-wrap">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]} // Support pour les tableaux, listes de tâches, etc.
          components={{
            // Personnaliser le rendu des éléments pour éviter les barres de défilement
            pre: ({ children }) => (
              <div className="whitespace-pre-wrap break-words">{children}</div>
            ),
            code: ({ className, children, inline, ...props }: any) => {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                // Blocs de code avec coloration syntaxique - sortir du contexte markdown-content
                <div className="code-block-container">
                  <SyntaxHighlighter
                    style={vscDarkPlus}
                    language={match[1]}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      fontSize: '0.875rem',
                      lineHeight: 1.4,
                      background: 'transparent', // Let container handle background
                      backgroundColor: 'transparent', // Force transparent background
                      fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    }}
                    wrapLines={true}
                    wrapLongLines={true}
                    useInlineStyles={true}
                    showLineNumbers={false}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                </div>
              ) : (
                // Code en ligne
                <code className="bg-gray-700 px-1 py-0.5 rounded text-sm text-gray-200" {...props}>
                  {children}
                </code>
              );
            },
            // Personnaliser le rendu des tableaux
            table: ({ children }) => (
              <div className="overflow-x-auto my-4">
                <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-600">{children}</table>
              </div>
            ),
            th: ({ children }) => (
              <th className="px-4 py-2 bg-gray-700 text-left text-xs font-bold text-white uppercase tracking-wider border border-gray-300 dark:border-gray-600">{children}</th>
            ),
            td: ({ children }) => (
              <td className="px-4 py-2 text-sm text-white border border-gray-300 dark:border-gray-600">{children}</td>
            ),
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>

      {/* Curseur clignotant */}
      {isStreaming && showCursor && (
        <span className="animate-pulse absolute bottom-0 right-0">▌</span>
      )}
    </div>
  );
};

export default MarkdownRenderer;
