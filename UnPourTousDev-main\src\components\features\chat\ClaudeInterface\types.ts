import { Id } from "../../../../../convex/_generated/dataModel";

/**
 * Interface pour les propriétés du composant ClaudeInterface
 */
export interface ClaudeInterfaceProps {
  onConversationCreated?: (id: Id<"conversations">) => void;
}

/**
 * Interface pour les propriétés du composant Sidebar
 */
export interface SidebarProps {
  activeButton: string;
  handleSidebarClick: (buttonType: string) => void;
  conversations: any[];
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void;
}

/**
 * Interface pour les propriétés du composant ChatArea
 */
export interface ChatAreaProps {
  chatStarted: boolean;
  conversationId: Id<"conversations"> | null;
  userMessage: string;
  assistantResponse: string;
  isLoading: boolean;
  renderWelcomeScreen: () => JSX.Element;
  renderChatScreen: () => JSX.Element;
}

/**
 * Interface pour les propriétés du composant MessageInput
 */
export interface MessageInputProps {
  userMessage: string;
  setUserMessage: (message: string) => void;
  isLoading: boolean;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  startChat: (modelId?: string) => void;
  // Ces props sont conservées pour la compatibilité avec les composants existants
  // mais ne sont plus utilisées directement dans le composant
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  useAutoRouter: boolean;
  setUseAutoRouter: (use: boolean) => void;
  // Props pour la sélection des modèles (passées depuis ClaudeInterface)
  models: AIModel[];
  activeSelection: string;
  openCategory: string | null;
  toggleCategory: (category: string) => void;
  handleAutoSelect: () => void;
  handleModelSelect: (modelId: string) => void;
}

/**
 * Interface pour les propriétés du composant ActionButtons
 * Le bouton AutoRouter a été déplacé vers un composant séparé
 */
export interface ActionButtonsProps {
  userMessage: string;
  isLoading: boolean;
  startChat: (modelId?: string) => void;
}

/**
 * Interface pour les propriétés du composant CategoryButtons
 */
export interface CategoryButtonsProps {
  openCategory: string | null;
  toggleCategory: (category: string) => void;
}

/**
 * Interface pour les propriétés du composant ModelSelector
 */
export interface ModelSelectorProps {
  models: any[];
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  openCategory: string | null;
}

/**
 * Interface pour les propriétés du composant LargeAutoRouterButton
 */
export interface LargeAutoRouterButtonProps {
  activeSelection: string;
  handleAutoSelect: () => void;
}

/**
 * Interface pour un modèle d'IA
 */
export interface AIModel {
  _id: Id<"livemodels">;
  _creationTime: number;
  modelId: string;
  name: string;
  provider: string;
  description?: string;
  enabled: boolean;
  chat?: boolean;
  webSearch?: boolean;
  reasoning?: boolean;
}

/**
 * Interface pour une conversation
 */
export interface Conversation {
  _id: Id<"conversations">;
  _creationTime: number;
  title: string;
  userId: string;
  usesAutoRouter?: boolean;
  lastModelUsed?: string;
  lastModelProvider?: string;
}

/**
 * Interface pour un message
 */
export interface Message {
  _id: Id<"messages">;
  _creationTime: number;
  conversationId: Id<"conversations">;
  role: "user" | "assistant";
  content: string;
  modelUsed?: string;
  modelName?: string;
  isStreaming?: boolean;
  references?: any[];
}
