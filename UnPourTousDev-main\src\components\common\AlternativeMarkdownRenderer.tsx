import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import mermaid from "mermaid";
import { processLatexInContent } from "../../utils/latexProcessor";
import LatexDebugger from "./LatexDebugger";

interface Reference {
  id: string;
  title: string;
  url: string;
}

interface AlternativeMarkdownRendererProps {
  content: string;
  references?: Reference[];
  isStreaming?: boolean;
  showCursor?: boolean;
}

// Composant pour les diagrammes Mermaid
const MermaidDiagram = ({ content }: { content: string }) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const [svg, setSvg] = React.useState<string>("");
  const [error, setError] = React.useState<string | null>(null);
  const [showCode, setShowCode] = React.useState<boolean>(false);

  React.useEffect(() => {
    if (ref.current && !showCode) {
      try {
        mermaid.initialize({
          startOnLoad: false,
          theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        });

        mermaid.render(`mermaid-${Date.now()}`, content)
          .then(({ svg }) => {
            setSvg(svg);
            setError(null);
          })
          .catch(err => {
            console.error("Mermaid rendering error:", err);
            setError("Erreur de rendu du diagramme");
          });
      } catch (err) {
        console.error("Mermaid initialization error:", err);
        setError("Erreur d'initialisation du diagramme");
      }
    }
  }, [content, showCode]);

  const toggleView = () => {
    setShowCode(!showCode);
  };

  return (
    <div className="relative border border-gray-300 dark:border-gray-700 rounded-lg overflow-hidden">
      {/* Bouton de basculement */}
      <button
        onClick={toggleView}
        className="absolute top-2 right-2 bg-gray-700 hover:bg-gray-600 text-white text-xs px-2 py-1 rounded z-10"
      >
        {showCode ? "Voir l'aperçu" : "Voir le code"}
      </button>

      {/* Affichage du code ou du diagramme */}
      {showCode ? (
        <div className="bg-gray-800 p-4 text-gray-200 font-mono text-sm whitespace-pre-wrap overflow-auto">
          {content}
        </div>
      ) : (
        <div className="mermaid-diagram p-4 overflow-auto" ref={ref}>
          {svg ? (
            <div dangerouslySetInnerHTML={{ __html: svg }} />
          ) : (
            <div className="text-center p-2">
              {error || "Chargement du diagramme..."}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Version alternative du MarkdownRenderer qui utilise le prétraitement manuel des formules LaTeX
 * au lieu de rehype-katex
 */
const AlternativeMarkdownRenderer: React.FC<AlternativeMarkdownRendererProps> = ({
  content,
  references,
  isStreaming = false,
  showCursor = false,
}) => {
  // Fonction pour traiter les références dans le contenu
  const processReferences = (content: string): string => {
    if (!references || references.length === 0) {
      return content;
    }

    // Remplacer les références [n] par des liens Markdown
    let processedContent = content;
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      processedContent = processedContent.replace(
        refRegex,
        `[${refNumber}](${ref.url} "${ref.title}")`
      );
    });

    return processedContent;
  };

  // Traiter les références dans le contenu
  const processedContent = processReferences(content);
  
  // Prétraiter le contenu pour LaTeX
  const processedContentWithLatex = processLatexInContent(processedContent);

  return (
    <>
      {/* Débogueur LaTeX - à supprimer après résolution des problèmes */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mb-2 text-xs">
          <summary className="cursor-pointer text-gray-500">Déboguer LaTeX</summary>
          <LatexDebugger content={content} />
        </details>
      )}
      
      <div className="markdown-content break-words whitespace-pre-wrap">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]} // Nécessaire pour interpréter le HTML généré par KaTeX
          components={{
            pre: ({ children }) => (
              <div className="whitespace-pre-wrap break-words">{children}</div>
            ),
            code: ({ className, children, inline, ...props }: any) => {
              const match = /language-(\w+)/.exec(className || '');

              // Gestion spéciale pour les diagrammes Mermaid
              if (match && match[1] === 'mermaid') {
                return (
                  <MermaidDiagram content={String(children).replace(/\n$/, '')} />
                );
              }

              return !inline && match ? (
                // Blocs de code avec coloration syntaxique
                <div className="rounded overflow-hidden my-1">
                  <SyntaxHighlighter
                    style={vscDarkPlus}
                    language={match[1]}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      padding: '0.75rem',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      lineHeight: 1.4,
                    }}
                    wrapLines={true}
                    wrapLongLines={true}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                </div>
              ) : (
                // Code en ligne
                <code className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>
                  {children}
                </code>
              );
            },
            // Personnaliser le rendu des tableaux
            table: ({ children }) => (
              <div className="overflow-x-auto my-2 rounded-lg border border-gray-300 dark:border-gray-700">
                <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">{children}</table>
              </div>
            ),
            // Paragraphes avec espacement selon les spécifications
            p: ({ children }) => (
              <p style={{ marginTop: '0.5em', marginBottom: '0.5em', lineHeight: 1.4 }}>{children}</p>
            ),
          }}
        >
          {processedContentWithLatex}
        </ReactMarkdown>
        {isStreaming && showCursor && (
          <span className="animate-pulse">▌</span>
        )}
      </div>
    </>
  );
};

export default AlternativeMarkdownRenderer;
