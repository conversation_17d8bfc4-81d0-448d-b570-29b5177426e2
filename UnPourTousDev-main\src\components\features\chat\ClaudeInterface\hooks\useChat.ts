import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { Id } from "../../../../../../convex/_generated/dataModel";
import { Conversation } from "../types";

/**
 * Hook personnalisé pour gérer l'état du chat
 * @param onConversationCreated - Fonction appelée lorsqu'une nouvelle conversation est créée
 */
export const useChat = (onConversationCreated?: (id: Id<"conversations">) => void) => {
  // État du chat
  const [userMessage, setUserMessage] = useState("");
  const [chatStarted, setChatStarted] = useState(false);
  const [assistantResponse, setAssistantResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null>(null);
  const [activeButton, setActiveButton] = useState("new"); // 'new', 'chat', ou 'settings'

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const modelSelectionRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Récupérer les conversations depuis Convex
  const conversations = useQuery(api.conversations.list) || [];

  // Mutation pour envoyer un message
  const sendMessage = useMutation(api.messages.send);

  // Récupérer les messages de la conversation actuelle
  const messages = useQuery(
    api.messages.list,
    currentConversationId ? { conversationId: currentConversationId } : "skip"
  ) || [];

  // Défilement automatique vers le bas
  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [assistantResponse, chatStarted, messages]);

  // Ajuster la hauteur du textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [userMessage]);

  // Gérer les clics sur les boutons de la sidebar
  const handleSidebarClick = (buttonType: string) => {
    setActiveButton(buttonType);

    if (buttonType === "new") {
      // Démarrer une nouvelle conversation
      setChatStarted(false);
      setUserMessage("");
      setAssistantResponse("");
      setCurrentConversationId(null);
    } else if (buttonType === "chat") {
      // Afficher l'historique des conversations (simulé)
      if (!chatStarted) {
        setChatStarted(true);
        setAssistantResponse("Voici l'historique de vos conversations. Vous pouvez reprendre une conversation ou en démarrer une nouvelle.");
      }
    } else if (buttonType === "settings") {
      // Afficher les paramètres (simulé)
      setChatStarted(true);
      setAssistantResponse("Paramètres de l'application : Vous pouvez configurer vos préférences ici.");
    }
  };

  // Gérer la sélection d'une conversation
  const handleSelectConversation = (id: Id<"conversations">) => {
    setCurrentConversationId(id);
    setChatStarted(true);
    setActiveButton("chat");

    // Si onConversationCreated est fourni, l'appeler
    if (onConversationCreated) {
      onConversationCreated(id);
    }
  };

  // Gérer la création d'une nouvelle conversation
  const handleNewConversation = () => {
    setChatStarted(false);
    setUserMessage("");
    setAssistantResponse("");
    setCurrentConversationId(null);
    setActiveButton("new");
  };

  // Gérer l'appui sur les touches dans le textarea
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (userMessage.trim() && !isLoading) {
        // Note: Cette fonction est remplacée dans MessageInput.tsx pour passer activeSelection
        // Nous la conservons ici pour la compatibilité avec d'autres composants
        startChat();
      }
    }
  };

  // Démarrer une conversation
  const startChat = async (selectedModelId?: string) => {
    if (!userMessage.trim() || isLoading) return;

    setIsLoading(true);
    setChatStarted(true);

    // Sauvegarder le message pour l'affichage
    const message = userMessage;

    // Effacer l'input
    setUserMessage("");
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }

    try {
      // Utiliser le modèle sélectionné ou AutoRouter par défaut
      // Assurons-nous que selectedModelId n'est pas undefined ou null
      const modelId = selectedModelId || "openrouter/auto";

      // Logs de diagnostic
      console.log("AUTOSELECT_ID:", "openrouter/auto");
      console.log("Sélection active (selectedModelId):", selectedModelId);
      console.log("modelId utilisé:", modelId);
      console.log("Est-ce AutoRouter?", modelId === "openrouter/auto");

      // Envoyer le message à Convex
      // Si currentConversationId est null, ne pas inclure ce paramètre (il sera undefined)
      const messageParams: {
        conversationId?: Id<"conversations">;
        content: string;
        modelId: string;
      } = {
        content: message,
        modelId: modelId,
      };

      // Log des arguments envoyés à la mutation Convex
      console.log("Arguments pour la mutation Convex:", messageParams);

      // Ajouter conversationId seulement s'il n'est pas null
      if (currentConversationId) {
        messageParams.conversationId = currentConversationId;
      }

      const newConversationId = await sendMessage(messageParams);

      // Mettre à jour l'ID de conversation si une nouvelle a été créée
      if (newConversationId && !currentConversationId) {
        setCurrentConversationId(newConversationId);

        // Si onConversationCreated est fourni, l'appeler
        if (onConversationCreated) {
          onConversationCreated(newConversationId);
        }
      }

      // Si nous n'utilisons pas Convex, simuler une réponse
      if (!newConversationId && !currentConversationId) {
        setTimeout(() => {
          setAssistantResponse("Bonjour ! Je suis votre assistant IA. Comment puis-je vous aider aujourd'hui ?");
          setIsLoading(false);
        }, 2000);
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
      setAssistantResponse("Une erreur s'est produite lors de l'envoi du message. Veuillez réessayer.");
      setIsLoading(false);
    }
  };

  return {
    userMessage,
    setUserMessage,
    chatStarted,
    setChatStarted,
    assistantResponse,
    setAssistantResponse,
    isLoading,
    setIsLoading,
    currentConversationId,
    setCurrentConversationId,
    activeButton,
    setActiveButton,
    textareaRef,
    modelSelectionRef,
    bottomRef,
    conversations,
    messages,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat
  };
};
