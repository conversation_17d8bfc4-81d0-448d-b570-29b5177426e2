"use client";

import React, { useState, useRef, ReactNode } from "react";
import { motion } from "framer-motion";

interface SpotlightBoxCursorProps {
  children: ReactNode;
  className?: string;
}

/**
 * Composant SpotlightBox avec effet de spotlight qui suit le curseur
 */
const SpotlightBoxCursor: React.FC<SpotlightBoxCursorProps> = ({ children, className = "" }) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!divRef.current) return;

    const div = divRef.current;
    const rect = div.getBoundingClientRect();

    setPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseEnter = () => {
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };

  return (
    <div
      ref={divRef}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`relative max-w-2xl w-full overflow-hidden rounded-xl border border-tango-300 dark:border-bronze-600 bg-white dark:bg-bronze-800 px-4 py-4 shadow-xl transition-colors duration-200 ${className}`}
    >
      <motion.div
        className="pointer-events-none absolute -inset-px rounded-xl opacity-0"
        style={{
          background: `radial-gradient(600px circle at ${position.x}px ${position.y}px, rgba(227, 115, 20, 0.4), rgba(220, 75, 4, 0.2) 40%, transparent 60%)`,
          opacity
        }}
      />
      {children}
    </div>
  );
};

export default SpotlightBoxCursor;