@echo off
echo ===================================
echo Lancement des applications UnPourTous
echo ===================================
echo.
echo Demarrage du backend Convex et des interfaces utilisateur...
echo.

REM Ouvre une nouvelle fenêtre CMD pour le backend Convex
start cmd /k "title UnPourTous Backend - Convex & echo Demarrage du backend Convex... & npx convex dev --typecheck=disable"

REM Attend 5 secondes pour que le backend démarre
echo Attente du démarrage du backend (5 secondes)...
timeout /t 5 /nobreak > nul

REM Ouvre une nouvelle fenêtre CMD pour l'UI frontend principale
start cmd /k "title UnPourTous Frontend - Vite & echo Demarrage de l'interface utilisateur principale... & npm run dev:frontend"

REM Ouvre une nouvelle fenêtre CMD pour l'UI admin
start cmd /k "cd admin & title UnPourTous Admin - Vite & echo Demarrage de l'interface d'administration... & npm run dev"

echo.
echo ===================================
echo Applications lancées avec succès !
echo ===================================
echo.
echo Le backend Convex et les interfaces utilisateur sont en cours d'exécution.
echo - Interface principale: http://localhost:5173
echo - Interface admin: http://localhost:5174
echo.
echo Pour arrêter les applications, fermez les fenêtres de terminal.
echo.
pause
