import { Id } from "../../convex/_generated/dataModel";

/**
 * Type représentant un modèle d'IA dans l'application
 */
export interface Model {
  _id: Id<"models">;
  _creationTime: number;
  name: string;
  modelId: string;
  provider: string;
  description?: string;
  enabled: boolean;
  capabilities?: {
    webSearch?: boolean;
    reasoning?: boolean;
    chat?: boolean;
  };
  category?: string;
  contextWindow?: number;
  maxTokens?: number;
  tokenizerId?: string;
  pricing?: {
    prompt?: number;
    completion?: number;
  };
}

/**
 * Type pour la création d'un nouveau modèle
 */
export type NewModel = {
  name: string;
  modelId: string;
  provider: string;
  description?: string;
  enabled: boolean;
  capabilities?: {
    webSearch?: boolean;
    reasoning?: boolean;
    chat?: boolean;
  };
  category?: string;
  contextWindow?: number;
  maxTokens?: number;
  tokenizerId?: string;
  pricing?: {
    prompt?: number;
    completion?: number;
  };
};
