import { useState, useRef, useEffect } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { Id } from "../../../../../../convex/_generated/dataModel";

/**
 * Hook personnalisé pour gérer la saisie et l'envoi des messages
 */
export const useMessageInput = (
  conversationId: Id<"conversations"> | null,
  onConversationCreated: (id: Id<"conversations">) => void,
  selectedModel: string
) => {
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Mutation Convex pour envoyer un message
  const sendMessage = useMutation(api.messages.send);

  // Ajuster la hauteur du textarea lorsque le contenu change
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const newHeight = Math.min(textareaRef.current.scrollHeight, 300);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [message]);

  // Fonction pour gérer la soumission du formulaire
  const handleSubmit = async () => {
    if (!message.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Envoyer le message pour créer une nouvelle conversation ou ajouter à une existante
      // Si conversationId est null, ne pas inclure ce paramètre (il sera undefined)
      const messageParams: {
        conversationId?: Id<"conversations">;
        content: string;
        modelId: string;
      } = {
        content: message,
        modelId: selectedModel,
      };

      // Ajouter conversationId seulement s'il n'est pas null
      if (conversationId) {
        messageParams.conversationId = conversationId;
      }

      const newConversationId = await sendMessage(messageParams);

      setMessage("");

      // Réinitialiser la hauteur du textarea à sa valeur par défaut
      if (textareaRef.current) {
        textareaRef.current.style.height = "44px"; // Hauteur minimale
        textareaRef.current.style.overflowY = "hidden"; // Masquer la barre de défilement
      }

      // Si une nouvelle conversation a été créée, mettre à jour l'ID de conversation
      if (newConversationId && !conversationId) {
        onConversationCreated(newConversationId);
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fonction pour gérer l'appui sur les touches
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // Fonction pour ajuster automatiquement la hauteur du textarea
  const adjustTextareaHeight = (e: React.FormEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    target.style.height = "auto";
    const newHeight = Math.min(target.scrollHeight, 300);
    target.style.height = `${newHeight}px`;

    // Afficher la barre de défilement uniquement si plus de 11 lignes
    const lineCount = target.value.split("\n").length;
    target.style.overflowY = lineCount > 11 ? "auto" : "hidden";
  };

  return {
    message,
    setMessage,
    isSubmitting,
    textareaRef,
    handleSubmit,
    handleKeyDown,
    adjustTextareaHeight
  };
};
