import React from 'react';
import MarkdownRenderer from '../components/common/MarkdownRenderer';

/**
 * Composant de test pour les tableaux Markdown utilisant react-markdown
 */
const TableTest: React.FC = () => {
  // Exemple de contenu Markdown avec un tableau
  const tableContent = `
# Test de tableau avec react-markdown

Voici un tableau de test simple :

| Catégorie | Élément | Statut    | Priorité |
| :-------- | :------ | :-------- | :------- |
| Tâche A   | Design  | En cours  | Haute    |
| Tâche B   | Dev     | À faire   | Moyenne  |
| Tâche C   | Test    | Terminé   | Basse    |
| Tâche D   | Doc     | En attente| Moyenne  |
`;

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Test de tableau avec react-markdown</h2>
      <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg">
        <MarkdownRenderer content={tableContent} />
      </div>
    </div>
  );
};

export default TableTest;
