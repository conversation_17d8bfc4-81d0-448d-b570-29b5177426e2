import { action, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";

export const list = query({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return [];

    // Vérifie que l'utilisateur a accès à la conversation
    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== identity.subject) {
      return [];
    }

    return await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) => q.eq("conversationId", args.conversationId))
      .order("asc")
      .collect();
  },
});

export const send = mutation({
  args: {
    content: v.string(),
    conversationId: v.optional(v.id("conversations")),
    modelId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    // Utiliser AutoRouter si aucun modèle n'est spécifié ou si le modèle est "openrouter/auto"
    const modelId = args.modelId || "openrouter/auto";
    const isAutoRouter = modelId === "openrouter/auto";

    // Récupère le nom du modèle à partir de la table livemodels
    const model = await ctx.db
      .query("livemodels")
      .filter((q) => q.eq(q.field("modelId"), modelId))
      .first();

    // Si le modèle est AutoRouter et n'est pas dans la table livemodels, utiliser un nom par défaut
    let modelName = model?.name;
    if (!modelName) {
      if (isAutoRouter) {
        modelName = "AutoRouter";
      } else {
        modelName = modelId;
      }
    }

    let conversationId = args.conversationId;

    if (conversationId) {
      // Si on utilise une conversation existante, utiliser le modèle déjà défini
      const conversation = await ctx.db.get(conversationId);
      if (!conversation) throw new Error("Conversation non trouvée");

      // Si la conversation n'a pas encore de modelId ou usesAutoRouter, les définir
      if (conversation.modelId === undefined || conversation.usesAutoRouter === undefined) {
        await ctx.db.patch(conversationId, {
          modelId: modelId,
          usesAutoRouter: isAutoRouter
        });
      }
    } else {
      // Crée une nouvelle conversation avec le modelId et usesAutoRouter
      conversationId = await ctx.db.insert("conversations", {
        userId: identity.subject,
        title: args.content.length > 50 ? args.content.slice(0, 50) + "..." : args.content,
        usesAutoRouter: isAutoRouter,
        modelId: modelId,
      });
    }

    // Sauvegarde le message utilisateur
    await ctx.db.insert("messages", {
      conversationId,
      role: "user",
      content: args.content,
      modelUsed: modelId,
      modelName: modelName,
    });

    // Déclenche la réponse de l'IA
    await ctx.scheduler.runAfter(0, api.ai.getResponse, {
      conversationId,
      modelId: modelId,
      modelName: modelName,
    });

    // Si c'est une nouvelle conversation, déclenche la génération du titre
    if (!args.conversationId) {
      // Utilise un délai pour permettre à la conversation de se créer complètement
      await ctx.scheduler.runAfter(1000, api.messages.generateTitle, {
        conversationId,
        firstMessageContent: args.content,
      });
    }

    return conversationId;
  },
});

// Sauvegarde un message de l'assistant
export const saveAssistantMessage = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    content: v.string(),
    modelId: v.string(),
    modelName: v.optional(v.string()),
    references: v.optional(v.array(v.object({
      id: v.string(),
      title: v.string(),
      url: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: "assistant",
      content: args.content,
      modelUsed: args.modelId,
      modelName: args.modelName,
      references: args.references,
    });
  },
});

// Crée un message de l'assistant en mode streaming
export const startStreamingMessage = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    modelId: v.string(),
    modelName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Crée un message vide avec isStreaming=true
    return await ctx.db.insert("messages", {
      conversationId: args.conversationId,
      role: "assistant",
      content: "",
      modelUsed: args.modelId,
      modelName: args.modelName,
      isStreaming: true,
    });
  },
});

// Met à jour un message en streaming avec du nouveau contenu
export const appendToStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
    contentToAppend: v.string(),
  },
  handler: async (ctx, args) => {
    // Récupère le message actuel
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message non trouvé");

    // Vérifie que le message est en streaming
    if (!message.isStreaming) throw new Error("Ce message n'est pas en mode streaming");

    // Ajoute le nouveau contenu
    await ctx.db.patch(args.messageId, {
      content: message.content + args.contentToAppend,
    });
  },
});

// Finalise un message en streaming
export const finishStreamingMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    // Marque le message comme n'étant plus en streaming
    await ctx.db.patch(args.messageId, {
      isStreaming: false,
    });
  },
});

// Met à jour un message de l'assistant (pour le streaming ou les modifications)
export const updateAssistantMessage = internalMutation({
  args: {
    messageId: v.id("messages"),
    content: v.optional(v.string()),
    modelId: v.optional(v.string()),
    modelName: v.optional(v.string()),
    isStreaming: v.optional(v.boolean()),
    references: v.optional(v.array(v.object({
      id: v.string(),
      title: v.string(),
      url: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    // Récupère le message actuel
    const message = await ctx.db.get(args.messageId);
    if (!message) throw new Error("Message non trouvé");

    // Prépare les champs à mettre à jour
    const updates: Record<string, any> = {};

    if (args.content !== undefined) updates.content = args.content;
    if (args.modelId !== undefined) updates.modelUsed = args.modelId;
    if (args.modelName !== undefined) updates.modelName = args.modelName;
    if (args.isStreaming !== undefined) updates.isStreaming = args.isStreaming;
    if (args.references !== undefined) updates.references = args.references;

    // Applique les mises à jour
    await ctx.db.patch(args.messageId, updates);

    return args.messageId;
  },
});

// Récupère les messages d'une conversation sans vérifier l'authentification (pour usage interne)
export const listInternal = internalQuery({
  args: {
    conversationId: v.id("conversations"),
  },
  handler: async (ctx, args) => {
    // Récupère tous les messages de la conversation sans vérifier l'authentification
    return await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) => q.eq("conversationId", args.conversationId))
      .order("asc")
      .collect();
  },
});

// Met à jour le titre d'une conversation
export const updateConversationTitle = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Met à jour le titre de la conversation
      await ctx.db.patch(args.conversationId, {
        title: args.title
      });

      return { success: true };
    } catch (error) {
      console.error("Erreur lors de la mise à jour du titre:", error);
      return { success: false };
    }
  },
});

// Génère un titre pour une conversation
export const generateTitle = action({
  args: {
    conversationId: v.id("conversations"),
    firstMessageContent: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Appelle l'API OpenRouter pour générer le titre avec le modèle Mistral Ministral 3B
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
          "HTTP-Referer": "https://unpourtouschat.com",
          "X-Title": "UnPourTous Chat"
        },
        body: JSON.stringify({
          model: "mistralai/ministral-3b",
          messages: [
            {
              role: "system",
              content: "Tu es un assistant spécialisé dans la création de titres concis et informatifs pour des conversations de chat.\nLe message suivant est le tout premier message envoyé par un utilisateur pour démarrer une nouvelle conversation.\nTa mission est de générer un titre de **3 à 5 mots maximum**, en **français**, pour cette conversation. Ce titre sera affiché dans une barre latérale (sidebar) pour permettre à l'utilisateur d'identifier rapidement le sujet de la discussion.\n\nLe titre doit capturer l'essence du message, le sujet principal, ou l'intention clé de l'utilisateur. Évite les phrases complètes ; pense à des mots-clés ou à une expression très courte.\n\nTitre suggéré:"
            },
            {
              role: "user",
              content: args.firstMessageContent
            }
          ],
          max_tokens: 20,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erreur OpenRouter:", errorText);
        throw new Error(`Erreur OpenRouter: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Réponse OpenRouter (action):", data);

      // Extrait le titre généré
      let generatedTitle = "";
      if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        generatedTitle = data.choices[0].message.content.trim();
      }

      // Nettoie le titre (supprime les guillemets, limite la longueur)
      generatedTitle = generatedTitle.replace(/^["']|["']$/g, "").trim();

      // Limite la longueur du titre (max 50 caractères)
      if (generatedTitle.length > 50) {
        generatedTitle = generatedTitle.substring(0, 47) + "...";
      }

      // Si le titre est vide ou trop court, utilise un extrait du message
      if (!generatedTitle || generatedTitle.length < 3) {
        console.warn("Titre généré invalide, utilisation d'un extrait du message");
        generatedTitle = args.firstMessageContent.length > 50
          ? args.firstMessageContent.slice(0, 47) + "..."
          : args.firstMessageContent;
      }

      console.log("Titre généré:", generatedTitle);

      // Met à jour le titre de la conversation via une mutation interne
      await ctx.runMutation(internal.messages.updateConversationTitle, {
        conversationId: args.conversationId,
        title: generatedTitle
      });

      return {
        success: true,
        title: generatedTitle
      };
    } catch (error) {
      console.error("Erreur lors de la génération du titre:", error);

      // En cas d'erreur, on utilise un extrait du message comme titre par défaut
      const fallbackTitle = args.firstMessageContent.length > 50
        ? args.firstMessageContent.slice(0, 47) + "..."
        : args.firstMessageContent;

      try {
        // Met à jour le titre avec le fallback
        await ctx.runMutation(internal.messages.updateConversationTitle, {
          conversationId: args.conversationId,
          title: fallbackTitle
        });
      } catch (updateError) {
        console.error("Erreur lors de la mise à jour du titre fallback:", updateError);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        title: fallbackTitle
      };
    }
  },
});




