import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Récupère tous les modèles en production
export const list = query({
  args: {},
  handler: async (ctx) => {
    const models = await ctx.db.query("livemodels").collect();
    return models.map(model => ({
      ...model,
      enabled: model.enabled ?? false // Provide a default value for 'enabled'
    }));
  },
});

// Récupère les modèles par fournisseur
export const listByProvider = query({
  args: {
    provider: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("livemodels")
      .filter((q) => q.eq(q.field("provider"), args.provider))
      .collect();
  },
});

// Fonction pour mettre à jour le nom d'un modèle en production
export const updateModelName = mutation({
  args: {
    modelId: v.id("livemodels"),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Vérifie si le modèle existe
    const model = await ctx.db.get(args.modelId);
    if (!model) {
      throw new Error(`Le modèle avec l'ID ${args.modelId} n'existe pas`);
    }

    // Met à jour le nom du modèle
    await ctx.db.patch(args.modelId, { name: args.name });

    return { success: true };
  },
});
