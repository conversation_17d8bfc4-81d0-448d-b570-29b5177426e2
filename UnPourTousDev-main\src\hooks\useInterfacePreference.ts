import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

/**
 * Hook personnalisé pour gérer les préférences d'interface
 * @returns {Object} Objet contenant les préférences d'interface et les fonctions pour les modifier
 */
export const useInterfacePreference = () => {
  // État local pour le type d'interface (avec valeur par défaut depuis localStorage)
  const [interfaceType, setInterfaceTypeState] = useState<string>(() => {
    const savedInterface = localStorage.getItem("interfaceType");
    return savedInterface || "claude"; // "claude" par défaut
  });
  
  // Récupérer les préférences utilisateur depuis Convex
  const userPreferences = useQuery(api.userPreferences.get);
  
  // Mutations Convex pour mettre à jour les préférences
  const setUserPreferences = useMutation(api.userPreferences.set);
  const setInterfaceTypePreference = useMutation(api.userPreferences.setInterfaceType);
  
  // Synchroniser avec Convex au chargement
  useEffect(() => {
    if (userPreferences && userPreferences.interfaceType) {
      // Si les préférences Convex existent, mettre à jour l'état local et localStorage
      setInterfaceTypeState(userPreferences.interfaceType);
      localStorage.setItem("interfaceType", userPreferences.interfaceType);
    } else if (interfaceType) {
      // Si les préférences Convex n'existent pas mais que l'état local existe,
      // mettre à jour Convex avec l'état local
      setInterfaceTypePreference({ interfaceType });
    }
  }, [userPreferences, interfaceType, setInterfaceTypePreference]);

  /**
   * Fonction pour changer le type d'interface
   * @param {string} type - Le nouveau type d'interface ("original" ou "claude")
   */
  const setInterfaceType = async (type: string) => {
    // Mettre à jour l'état local
    setInterfaceTypeState(type);
    
    // Mettre à jour localStorage
    localStorage.setItem("interfaceType", type);
    
    // Mettre à jour Convex
    await setInterfaceTypePreference({ interfaceType: type });
  };

  return {
    interfaceType,
    setInterfaceType,
    isLoading: userPreferences === undefined,
  };
};

export default useInterfacePreference;
