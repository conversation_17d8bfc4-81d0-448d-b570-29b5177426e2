import { useEffect, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import SourceTile from "./SourceTile";
import MoreSourcesTile from "./MoreSourcesTile";
import ModelIcon from "./ModelIcon";
import MarkdownRenderer from "../components/common/MarkdownRenderer";

interface Reference {
  id: string;
  title: string;
  url: string;
}

interface AiMessageProps {
  message: {
    _id: string;
    content: string;
    modelUsed: string;
    modelName?: string;
    isStreaming?: boolean;
    references?: Reference[];
  };
}



export default function AiMessage({ message }: AiMessageProps) {
  // État pour l'animation du curseur
  const [showCursor, setShowCursor] = useState(true);

  // Utilise directement le nom du modèle stocké dans le message, ou récupère-le depuis la table livemodels
  const models = useQuery(api.livemodels.list) || [];
  const model = models.find(m => m.modelId === message.modelUsed);
  const modelName = message.modelName || model?.name || message.modelUsed;
  const isWebSearchModel = model?.webSearch === true;

  // Détermine si le message a été généré par AutoRouter
  const isAutoRouter = message.modelUsed === "openrouter/auto";

  // Animation du curseur clignotant
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);

      return () => clearInterval(interval);
    } else {
      setShowCursor(false);
    }
  }, [message.isStreaming]);

  // État pour afficher toutes les sources ou seulement les 3 premières
  const [showAllSources, setShowAllSources] = useState(false);

  // Nous n'avons plus besoin de prétraiter le contenu, car cela est géré par le composant MarkdownRenderer

  return (
    <div className="flex mb-4 w-full">
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg py-2 px-4 max-w-[95%] text-gray-800 dark:text-gray-200">
        <div className="flex items-center mb-1">
          <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex items-center">
            <ModelIcon
              model={isAutoRouter ? "openrouter/auto" : message.modelUsed}
              provider={isAutoRouter ? "openrouter" : undefined}
              size={16}
              className="mr-1.5"
            />
            IA ({modelName})
            {isWebSearchModel && (
              <span className="ml-1 flex items-center text-xs text-blue-500 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-1.5 py-0.5 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9" />
                </svg>
                recherche web
              </span>
            )}
          </span>

          {/* Aucun bouton pour afficher/masquer les sources - elles sont toujours visibles */}
        </div>

        <div className="prose dark:prose-invert prose-sm max-w-none markdown-content w-full break-words whitespace-pre-wrap">
          {/* Affichage des références en haut sous forme de tuiles (toujours visibles) */}
          {message.references && message.references.length > 0 && (
            <div className="mb-3 pt-1 pb-2 border-b border-gray-300 dark:border-gray-600">
              <h4 className="text-xs font-medium mb-1">Sources:</h4>
              <div className="sources-row flex flex-wrap w-full gap-1">
                {/* Affichage des 3 premières sources (28% chacune) */}
                {message.references.slice(0, Math.min(3, message.references.length)).map((ref) => (
                  <div key={ref.id} className="flex-[2.8] min-w-0">
                    <SourceTile source={ref} />
                  </div>
                ))}

                {/* Affichage de la tuile "+X sources" si plus de 3 sources (16%) */}
                {message.references.length > 3 && (
                  <div className="flex-[1.6] min-w-0">
                    <MoreSourcesTile
                      sources={message.references.slice(3)}
                      onClick={() => setShowAllSources(!showAllSources)}
                    />
                  </div>
                )}

                {/* Remplir l'espace si moins de 3 sources */}
                {message.references.length === 1 && (
                  <div className="flex-[5.6]"></div>
                )}
                {message.references.length === 2 && (
                  <div className="flex-[2.8]"></div>
                )}
              </div>

              {/* Affichage de toutes les sources si showAllSources est true */}
              {showAllSources && (
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 sources-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {message.references.slice(3).map((ref) => (
                    <div key={ref.id}>
                      <SourceTile source={ref} />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Contenu du message avec le nouveau composant MarkdownRenderer */}
          <MarkdownRenderer
            content={message.content}
            references={message.references}
            isStreaming={message.isStreaming}
            showCursor={showCursor}
          />
        </div>
      </div>
    </div>
  );
}
