/**
 * Configuration des catégories de modèles
 */

export type ModelCategory = {
  id: string;
  name: string;
  description: string;
  color: string;
};

export const MODEL_CATEGORIES: ModelCategory[] = [
  {
    id: 'chat',
    name: '<PERSON><PERSON>',
    description: 'Modèles optimisés pour les conversations',
    color: 'bg-blue-500',
  },
  {
    id: 'reasoning',
    name: 'Raisonnement',
    description: 'Modèles avec de fortes capacités de raisonnement',
    color: 'bg-purple-500',
  },
  {
    id: 'webSearch',
    name: 'Recherche Web',
    description: 'Modèles avec accès à Internet',
    color: 'bg-green-500',
  },
];

/**
 * Obtient la catégorie d'un modèle en fonction de ses capacités
 * @param capabilities Les capacités du modèle
 * @returns La catégorie principale du modèle
 */
export const getModelCategory = (capabilities?: {
  webSearch?: boolean;
  reasoning?: boolean;
  chat?: boolean;
}): string => {
  if (!capabilities) return 'chat';
  
  if (capabilities.webSearch) return 'webSearch';
  if (capabilities.reasoning) return 'reasoning';
  return 'chat';
};
