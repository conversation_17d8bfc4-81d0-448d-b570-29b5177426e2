import { Id } from "../../../../../convex/_generated/dataModel";

/**
 * Interface pour les propriétés du composant NewConversationForm
 */
export interface NewConversationFormProps {
  onConversationCreated: (id: Id<"conversations">) => void;
}

/**
 * Interface pour un modèle d'IA
 */
export interface AIModel {
  _id: Id<"livemodels">;
  _creationTime: number;
  modelId: string;
  name: string;
  provider: string;
  description?: string;
  enabled: boolean;
  chat?: boolean;
  webSearch?: boolean;
  reasoning?: boolean;
}

/**
 * Interface pour les propriétés du composant MessageTextarea
 */
export interface MessageTextareaProps {
  message: string;
  setMessage: (message: string) => void;
  isSubmitting: boolean;
}

/**
 * Interface pour les propriétés du composant ModelButton
 */
export interface ModelButtonProps {
  model: AIModel;
  isSelected: boolean;
  onClick: () => void;
}

/**
 * Interface pour les propriétés du composant ModelCategory
 */
export interface ModelCategoryProps {
  category: string;
  title: string;
  models: AIModel[];
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  openCategory: string | null;
  toggleCategory: (category: string) => void;
}

/**
 * Interface pour les propriétés du composant ModelSelector
 */
export interface ModelSelectorProps {
  models: AIModel[];
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  openCategory: string | null;
  toggleCategory: (category: string) => void;
}

/**
 * Interface pour les propriétés du composant AutoRouterButton
 */
export interface AutoRouterButtonProps {
  isSelected: boolean;
  onClick: () => void;
}
