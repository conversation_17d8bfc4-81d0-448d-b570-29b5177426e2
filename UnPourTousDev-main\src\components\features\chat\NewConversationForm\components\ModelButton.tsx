import React from "react";
import { ModelButtonProps } from "../types";
import { ModelIcon } from "../../../../features/models";

/**
 * Composant pour un bouton de modèle individuel
 */
const ModelButton: React.FC<ModelButtonProps> = ({ model, isSelected, onClick }) => {
  // Utilise directement le nom du modèle tel qu'il est stocké dans la table
  const getCleanModelName = (model: any) => {
    return model.name;
  };

  // Déterminer les classes CSS en fonction de la catégorie et de la sélection
  const getButtonClasses = () => {
    const baseClasses = "flex items-center p-3 rounded-lg text-sm";

    if (isSelected) {
      return `${baseClasses} bg-bronze-700 text-white dark:bg-bronze-800 ring-2 ring-bronze-600 dark:ring-tango-500`;
    }

    if (model.chat) {
      return `${baseClasses} bg-bronze-100 text-bronze-700 hover:bg-bronze-200 dark:bg-bronze-700 dark:text-tango-200 dark:hover:bg-bronze-600`;
    }

    if (model.webSearch) {
      return `${baseClasses} bg-tango-50 text-tango-800 hover:bg-tango-100 dark:bg-tango-900/30 dark:text-tango-200 dark:hover:bg-tango-900/50`;
    }

    if (model.reasoning) {
      return `${baseClasses} bg-fire-50 text-fire-800 hover:bg-fire-100 dark:bg-fire-900/30 dark:text-fire-200 dark:hover:bg-fire-900/50`;
    }

    return `${baseClasses} bg-bronze-100 text-bronze-700 hover:bg-bronze-200 dark:bg-bronze-700 dark:text-tango-200 dark:hover:bg-bronze-600`;
  };

  return (
    <button
      type="button"
      onClick={(e) => {
        e.preventDefault(); // Empêcher la soumission du formulaire
        onClick();
      }}
      className={getButtonClasses()}
    >
      <ModelIcon
        model={model.modelId}
        provider={model.provider}
        size={24}
        className="mr-2 flex-shrink-0"
      />
      <span className="truncate font-medium">{getCleanModelName(model)}</span>
    </button>
  );
};

export default ModelButton;
