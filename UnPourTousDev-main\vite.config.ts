import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// Configuration pour React Compiler
const ReactCompilerConfig = {
  // Options de configuration du compilateur
};

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [
          ["babel-plugin-react-compiler", ReactCompilerConfig],
        ],
      },
    }),
  ],
  // Autres configurations Vite...
});

