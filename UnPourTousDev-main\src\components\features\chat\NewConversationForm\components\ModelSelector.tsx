import React from "react";
import { ModelSelectorProps, AIModel } from "../types";
import ModelCategory from "./ModelCategory";
import AutoRouterButton from "./AutoRouterButton";

/**
 * Composant pour la sélection des modèles
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModel,
  setSelectedModel,
  openCategory,
  toggleCategory
}) => {
  // Filtrer les modèles par catégorie
  const chatModels = models.filter((model: AIModel) => model.chat === true);
  const webSearchModels = models.filter((model: AIModel) => model.webSearch === true);
  const reasoningModels = models.filter((model: AIModel) => model.reasoning === true);

  return (
    <>
      {/* AutoRouter option */}
      <AutoRouterButton
        isSelected={selectedModel === "openrouter/auto"}
        onClick={() => setSelectedModel("openrouter/auto")}
      />

      {/* Trier les modèles par catégorie */}
      <div className="mt-2 space-y-4">
        {/* Catégorie 1: Modèles de chat standard */}
        {chatModels.length > 0 && (
          <ModelCategory
            category="chat"
            title="Chat"
            models={chatModels}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            openCategory={openCategory}
            toggleCategory={toggleCategory}
          />
        )}

        {/* Catégorie 2: Modèles avec recherche web */}
        {webSearchModels.length > 0 && (
          <ModelCategory
            category="web_search"
            title="Recherche Web"
            models={webSearchModels}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            openCategory={openCategory}
            toggleCategory={toggleCategory}
          />
        )}

        {/* Catégorie 3: Modèles avec raisonnement avancé */}
        {reasoningModels.length > 0 && (
          <ModelCategory
            category="reasoning"
            title="Raisonnement"
            models={reasoningModels}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            openCategory={openCategory}
            toggleCategory={toggleCategory}
          />
        )}
      </div>
    </>
  );
};

export default ModelSelector;
