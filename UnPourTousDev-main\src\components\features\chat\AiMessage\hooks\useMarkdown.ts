import { marked } from "marked";
import { Reference } from "../types";

// Configuration de marked pour qu'il retourne une chaîne synchrone
marked.setOptions({ async: false });

/**
 * Hook personnalisé pour gérer le formatage Markdown
 */
export const useMarkdown = () => {
  /**
   * Fonction pour transformer le contenu Markdown et créer des liens pour les références [n]
   */
  const processContent = (content: string, references?: Reference[]) => {
    // Configurer marked pour éviter les barres de défilement
    marked.setOptions({
      async: false,
      breaks: true, // Convertir les retours à la ligne en <br>
      gfm: true, // Activer GitHub Flavored Markdown (important pour les tableaux)
    });

    // Créer un nouveau renderer
    const renderer = new marked.Renderer();

    // Personnaliser le rendu des blocs de code
    // @ts-ignore - Ignorer les erreurs de typage pour la compatibilité avec marked
    renderer.code = function(code, language) {
      return `<div class="bg-gray-800 p-2 rounded whitespace-pre-wrap break-words my-2"><code class="${language ? `language-${language}` : ''}">${code}</code></div>`;
    };

    // Personnaliser le rendu des tableaux
    // @ts-ignore - Ignorer les erreurs de typage pour la compatibilité avec marked
    renderer.table = function(header, body) {
      return `<div class="overflow-x-auto my-4"><table class="w-full table-auto border-collapse border border-gray-300 dark:border-gray-600">${header}${body}</table></div>`;
    };

    // Appliquer le renderer personnalisé
    marked.setOptions({ renderer });

    // Si pas de références, convertir le Markdown en HTML directement
    if (!references || references.length === 0) {
      return { __html: marked.parse(content) as string };
    }

    // D'abord, convertir le Markdown en HTML
    let htmlContent = marked.parse(content) as string;

    // Ensuite, remplacer les références [n] par des liens HTML
    references.forEach((ref, index) => {
      const refNumber = index + 1;
      const refRegex = new RegExp(`\\[${refNumber}\\]`, 'g');
      htmlContent = htmlContent.replace(
        refRegex,
        `<a href="${ref.url}" target="_blank" rel="noopener noreferrer" class="reference-link" title="${ref.title}">[${refNumber}]</a>`
      );
    });

    return { __html: htmlContent };
  };

  return {
    processContent
  };
};
