@echo off
echo ===================================
echo Lancement de l'application VOtest
echo ===================================
echo.
echo Demarrage du backend Convex et de l'UI...
echo.

@echo off
REM Se déplace dans le répertoire où se trouve le script .bat lui-même
cd /d "%~dp0"
REM Maintenant, vous êtes sûr d'être dans D:\Projet_IA\UnPourTousDev-main\


REM Ouvre une nouvelle fenêtre CMD pour le backend Convex
start cmd /k "title VOtest Backend - Convex & echo Demarrage du backend Convex... & npx convex dev --typecheck=disable"

REM Attend 5 secondes pour que le backend démarre
echo Attente du démarrage du backend (5 secondes)...
timeout /t 5 /nobreak > nul

REM Ouvre une nouvelle fenêtre CMD pour l'UI frontend
start cmd /k "title VOtest Frontend - Vite & echo Demarrage de l'interface utilisateur... & npm run dev:frontend"

echo.
echo ===================================
echo Application lancée avec succès !
echo ===================================
echo.
echo Le backend Convex et l'interface utilisateur sont en cours d'exécution.
echo Pour arrêter l'application, fermez les fenêtres de terminal.
echo.
pause
