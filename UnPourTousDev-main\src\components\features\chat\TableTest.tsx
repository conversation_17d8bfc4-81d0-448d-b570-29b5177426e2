import React from 'react';
import MarkdownRenderer from './AiMessage/components/MarkdownRenderer';

/**
 * Composant de test pour vérifier le rendu des tableaux Markdown
 */
const TableTest: React.FC = () => {
  // Exemple de contenu Markdown avec un tableau
  const tableContent = `
# Test de rendu de tableau

Voici un exemple de tableau Markdown :

| Catégorie | Élément | Statut    | Priorité |
| :-------- | :------ | :-------- | :------- |
| Tâche A   | Design  | En cours  | Haute    |
| Tâche B   | Dev     | À faire   | Moyenne  |
| Tâche C   | Test    | Terminé   | Basse    |
| Tâche D   | Doc     | En attente| Moyenne  |

Et voici un autre tableau plus complexe :

| Fonction | Description | Paramètres | Retour |
|----------|-------------|------------|--------|
| \`getData()\` | Récupère les données | Aucun | Array<Object> |
| \`setConfig(options)\` | Configure l'application | \`options\`: Object | Boolean |
| \`processItem(item, index)\` | Traite un élément | \`item\`: Object<br>\`index\`: Number | Object |

## Test de code

\`\`\`javascript
// Exemple de code
function testFunction() {
  const data = [1, 2, 3];
  return data.map(item => item * 2);
}
\`\`\`
`;

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Test de rendu de tableau</h2>
      <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg">
        <MarkdownRenderer 
          content={tableContent}
          isStreaming={false}
          showCursor={false}
        />
      </div>
    </div>
  );
};

export default TableTest;
