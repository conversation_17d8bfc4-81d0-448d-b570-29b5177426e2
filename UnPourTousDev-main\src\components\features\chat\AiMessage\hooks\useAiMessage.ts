import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AiMessageProps } from "../types";

/**
 * Hook personnalisé pour gérer l'état et la logique d'un message IA
 */
export const useAiMessage = (message: AiMessageProps["message"]) => {
  // État pour l'animation du curseur
  const [showCursor, setShowCursor] = useState(true);

  // État pour afficher toutes les sources ou seulement les 3 premières
  const [showAllSources, setShowAllSources] = useState(false);

  // Récupère les informations sur les modèles
  const models = useQuery(api.livemodels.list) || [];

  // Log pour debug si les modèles ne sont pas chargés
  if (models.length === 0) {
    console.log("⚠️ [useAiMessage] Aucun modèle chargé depuis livemodels");
  }

  // Recherche flexible du modèle (correspondance exacte puis partielle)
  let model = models.find(m => m.modelId === message.modelUsed);

  // Si pas trouvé, recherche par correspondance partielle (pour les variantes de modèles)
  if (!model && message.modelUsed) {
    // Extraire la partie après le "/" pour les modèles OpenAI/Anthropic/etc.
    const modelPart = message.modelUsed.split('/')[1] || message.modelUsed;

    model = models.find(m => {
      // Correspondance directe
      if (m.modelId === message.modelUsed) return true;

      // Correspondance partielle - le modelId contient une partie du nom technique
      if (message.modelUsed.includes(m.modelId)) return true;

      // Correspondance inverse - le nom technique contient le modelId
      if (m.modelId.includes(modelPart)) return true;

      // Correspondance par nom de base (ex: gpt-4o dans openai/gpt-4o-2024-08-06)
      const baseModelName = modelPart.split('-')[0]; // ex: "gpt" de "gpt-4o-2024-08-06"
      if (m.modelId.toLowerCase().includes(baseModelName.toLowerCase())) return true;

      return false;
    });
  }

  // Priorité au nom d'affichage depuis livemodels, puis nom stocké dans le message
  const modelName = model?.name || message.modelName || message.modelUsed;

  // Debug supprimé - correspondance flexible implémentée

  // Détermine si le modèle a la capacité de recherche web
  const isWebSearchModel = model?.webSearch === true;

  // Détermine si le message a été généré par AutoRouter
  const isAutoRouter = message.modelUsed === "openrouter/auto";

  // Détermine si le modèle supporte le raisonnement
  // Pour AutoRouter, on vérifie s'il y a du contenu de raisonnement réel
  // Pour les autres modèles, on utilise la propriété reasoning
  const isReasoningModel = isAutoRouter
    ? Boolean(message.reasoning_content) // AutoRouter : seulement si du raisonnement existe
    : model?.reasoning === true; // Autres modèles : selon leur capacité

  // Animation du curseur clignotant
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);

      return () => clearInterval(interval);
    } else {
      setShowCursor(false);
    }
  }, [message.isStreaming]);

  return {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter,
    isReasoningModel
  };
};
