import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AiMessageProps } from "../types";

/**
 * Hook personnalisé pour gérer l'état et la logique d'un message IA
 */
export const useAiMessage = (message: AiMessageProps["message"]) => {
  // État pour l'animation du curseur
  const [showCursor, setShowCursor] = useState(true);

  // État pour afficher toutes les sources ou seulement les 3 premières
  const [showAllSources, setShowAllSources] = useState(false);

  // Récupère les informations sur les modèles
  const models = useQuery(api.livemodels.list) || [];
  const model = models.find(m => m.modelId === message.modelUsed);

  // Utilise directement le nom du modèle stocké dans le message, ou récupère-le depuis la table livemodels
  const modelName = message.modelName || model?.name || message.modelUsed;

  // Détermine si le modèle a la capacité de recherche web
  const isWebSearchModel = model?.webSearch === true;

  // Détermine si le message a été généré par AutoRouter
  const isAutoRouter = message.modelUsed === "openrouter/auto";

  // Détermine si le modèle supporte le raisonnement
  // Pour AutoRouter, on vérifie s'il y a du contenu de raisonnement réel
  // Pour les autres modèles, on utilise la propriété reasoning
  const isReasoningModel = isAutoRouter
    ? Boolean(message.reasoning_content) // AutoRouter : seulement si du raisonnement existe
    : model?.reasoning === true; // Autres modèles : selon leur capacité

  // Animation du curseur clignotant
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);

      return () => clearInterval(interval);
    } else {
      setShowCursor(false);
    }
  }, [message.isStreaming]);

  return {
    showCursor,
    showAllSources,
    setShowAllSources,
    modelName,
    isWebSearchModel,
    isAutoRouter,
    isReasoningModel
  };
};
