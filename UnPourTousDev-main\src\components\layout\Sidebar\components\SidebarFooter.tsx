import React from "react";
import { SidebarFooterProps } from "../types";
import { useUser } from "@clerk/clerk-react";

/**
 * Composant pour le pied de page de la sidebar
 */
const SidebarFooter: React.FC<SidebarFooterProps> = ({ 
  isCollapsed, 
  onOpenSettingsMenu,
  settingsMenuOpen
}) => {
  const { user } = useUser();

  return (
    <div className={`p-3 border-t border-gray-200 dark:border-gray-700 flex flex-col ${isCollapsed ? 'mt-auto items-center' : ''} relative`}>
      <button
        onMouseDown={onOpenSettingsMenu}
        onClick={(e) => e.stopPropagation()} // Empêche la propagation du clic
        className={`${isCollapsed ? 'p-2 rounded-full' : 'p-2 rounded-lg flex items-center'} ${
          settingsMenuOpen
            ? "bg-gray-200 dark:bg-gray-700" // Style de survol appliqué quand le menu est ouvert
            : "hover:bg-gray-200 dark:hover:bg-gray-700"
        } transition-colors mb-2`}
        title="Paramètres"
      >
        {isCollapsed ? (
          // Version rétractée - juste l'icône
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        ) : (
          <>
            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 font-bold mr-2">
              {user?.firstName?.[0] || user?.emailAddresses?.[0]?.emailAddress?.[0]?.toUpperCase() || '?'}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {user?.fullName || user?.emailAddresses?.[0]?.emailAddress || 'Utilisateur'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Paramètres</p>
            </div>
            {/* Utilisation de la même icône que dans la version rétractée */}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </>
        )}
      </button>
    </div>
  );
};

export default SidebarFooter;
