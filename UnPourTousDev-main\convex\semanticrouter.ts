import { action } from "./_generated/server";
import { v } from "convex/values";

// Fonction pour déterminer si un message nécessite une recherche web via Semantic-router
export const needsWebSearch = action({
  args: {
    message: v.string(),
    availableModels: v.array(
      v.object({
        modelId: v.string(),
        name: v.string(),
        provider: v.string(),
        webSearch: v.optional(v.boolean()),
        reasoning: v.optional(v.boolean()),
      })
    ),
  },
  handler: async (ctx, args): Promise<{ modelId: string; modelName: string; success: boolean }> => {
    try {
      // Récupère la clé API depuis les variables d'environnement
      const apiKey = process.env.AURELIO_API_KEY;
      if (!apiKey) {
        throw new Error("La clé API Aurelio n'est pas configurée");
      }

      // Définit les routes pour la classification
      const routes = [
        {
          name: "web_search_needed",
          utterances: [
            "Quelle est la météo aujourd'hui?",
            "Quelles sont les dernières nouvelles?",
            "Qui a gagné le match hier?",
            "Quel est le cours de l'action Apple?",
            "Quand a été fondée la société Microsoft?",
            "Qui est le président actuel de la France?",
            "Quels sont les événements récents à Paris?",
            "Donnez-moi des informations sur le dernier iPhone",
            "Quelles sont les critiques du film sorti cette semaine?",
            "Quels sont les résultats des élections?",
            "Quelle est la population actuelle de Tokyo?",
            "Quand aura lieu le prochain événement sportif majeur?",
            "Quelles sont les dernières découvertes scientifiques?",
            "Quel est le taux de change actuel entre l'euro et le dollar?",
            "Quels sont les meilleurs restaurants à New York?",
            "Quelles sont les actualités technologiques récentes?",
            "Qui a remporté le prix Nobel cette année?",
            "Quels sont les symptômes du COVID-19?",
            "Quand sort le prochain film Marvel?",
            "Quelle est la recette la plus populaire en ce moment?"
          ]
        },
        {
          name: "reasoning_needed",
          utterances: [
            "Explique-moi le concept de relativité",
            "Quelle est la différence entre machine learning et deep learning?",
            "Explique-moi comment fonctionne un moteur à combustion",
            "Quels sont les avantages et inconvénients du télétravail?",
            "Aide-moi à résoudre cette équation mathématique",
            "Explique-moi le concept de programmation orientée objet",
            "Quelles sont les meilleures pratiques en matière de cybersécurité?",
            "Explique-moi le fonctionnement d'un algorithme de tri",
            "Explique-moi les principes de base de l'économie",
            "Quelle est la différence entre corrélation et causalité?",
            "Comment fonctionne un réseau neuronal?",
            "Explique-moi la théorie de l'évolution",
            "Quels sont les principes fondamentaux de la thermodynamique?",
            "Comment fonctionne la cryptographie à clé publique?",
            "Explique-moi le paradoxe du chat de Schrödinger",
            "Quelles sont les implications éthiques de l'intelligence artificielle?",
            "Comment fonctionne le système immunitaire humain?",
            "Explique-moi le concept de développement durable",
            "Quels sont les principes de base de la psychologie cognitive?",
            "Comment fonctionne un moteur de recherche?"
          ]
        },
        {
          name: "general_chat",
          utterances: [
            "Comment vas-tu?",
            "Raconte-moi une blague",
            "Écris un poème sur l'amour",
            "Aide-moi à rédiger un email professionnel",
            "Donne-moi des idées pour mon anniversaire",
            "Comment puis-je améliorer ma productivité?",
            "Donne-moi des conseils pour apprendre une nouvelle langue",
            "Comment puis-je gérer mon stress?",
            "Aide-moi à créer un plan d'entraînement sportif",
            "Comment puis-je améliorer mes compétences en communication?",
            "Donne-moi des idées de recettes végétariennes",
            "Écris-moi une histoire courte",
            "Suggère-moi des activités pour le weekend",
            "Comment puis-je mieux organiser mon temps?",
            "Donne-moi des conseils pour mieux dormir",
            "Aide-moi à formuler une demande d'augmentation",
            "Propose-moi des idées de cadeaux d'anniversaire",
            "Comment puis-je commencer à méditer?",
            "Donne-moi des conseils pour réduire mon stress",
            "Aide-moi à rédiger une lettre de motivation"
          ]
        }
      ];

      // Appelle l'API Aurelio pour déterminer la catégorie du message
      const response = await fetch("https://api.aurelio.ai/semantic-router/route", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          input: args.message,
          routes: routes
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erreur Semantic-router:", errorText);
        throw new Error(`Erreur Semantic-router: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Réponse Semantic-router:", data);

      // Détermine le modèle à utiliser en fonction de la catégorie
      let selectedModelId: string;
      let selectedModelName: string;

      if (data.route === "web_search_needed") {
        // Cherche un modèle avec capacité de recherche web
        const webSearchModel = args.availableModels.find(m => m.webSearch === true);
        if (webSearchModel) {
          selectedModelId = webSearchModel.modelId;
          selectedModelName = webSearchModel.name;
        } else {
          // Fallback sur Perplexity si disponible
          const perplexityModel = args.availableModels.find(m => m.modelId.includes("perplexity"));
          if (perplexityModel) {
            selectedModelId = perplexityModel.modelId;
            selectedModelName = perplexityModel.name;
          } else {
            // Sinon, utilise AutoRouter
            selectedModelId = "openrouter/auto";
            selectedModelName = "AutoRouter";
          }
        }
      } else if (data.route === "reasoning_needed") {
        // Cherche un modèle avec capacité de raisonnement
        const reasoningModel = args.availableModels.find(m => m.reasoning === true);
        if (reasoningModel) {
          selectedModelId = reasoningModel.modelId;
          selectedModelName = reasoningModel.name;
        } else {
          // Sinon, utilise AutoRouter
          selectedModelId = "openrouter/auto";
          selectedModelName = "AutoRouter";
        }
      } else {
        // Pour les messages généraux, utilise AutoRouter
        selectedModelId = "openrouter/auto";
        selectedModelName = "AutoRouter";
      }

      console.log(`Semantic-router recommande la catégorie: ${data.route}, modèle choisi: ${selectedModelId} (${selectedModelName})`);

      return {
        modelId: selectedModelId,
        modelName: selectedModelName,
        success: true
      };
    } catch (error) {
      console.error("Erreur lors de l'appel à Semantic-router:", error);
      // En cas d'erreur, retourne l'AutoRouter par défaut
      return {
        modelId: "openrouter/auto",
        modelName: "AutoRouter",
        success: false
      };
    }
  },
});
