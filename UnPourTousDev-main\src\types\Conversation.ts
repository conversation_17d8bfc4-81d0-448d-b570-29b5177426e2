import { Id } from "../../convex/_generated/dataModel";

/**
 * Type représentant une conversation dans l'application
 */
export interface Conversation {
  _id: Id<"conversations">;
  _creationTime: number;
  userId: string;
  title: string;
  folderId?: Id<"folders">;
}

/**
 * Type pour la création d'une nouvelle conversation
 */
export type NewConversation = {
  title: string;
  folderId?: Id<"folders">;
};
