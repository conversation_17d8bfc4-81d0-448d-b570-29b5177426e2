import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { AIModel } from "../types";

/**
 * Hook personnalisé pour gérer la sélection des modèles
 */
export const useModelSelection = () => {
  const [selectedModel, setSelectedModel] = useState("openrouter/auto");
  const [selectedProvider, setSelectedProvider] = useState("openrouter");
  const [openCategory, setOpenCategory] = useState<string | null>(null);
  
  // Récupérer la liste des modèles depuis Convex
  const models = useQuery(api.livemodels.list) || [];

  // Fonction pour basculer l'état d'une catégorie
  const toggleCategory = (category: string) => {
    if (openCategory === category) {
      setOpenCategory(null); // Fermer si déjà ouvert
    } else {
      setOpenCategory(category); // Ouvrir cette catégorie
    }
  };

  // Mettre à jour le fournisseur lorsque le modèle sélectionné change
  useEffect(() => {
    if (selectedModel) {
      const model = models.find((m: AIModel) => m.modelId === selectedModel);
      if (model) {
        setSelectedProvider(model.provider);

        // Déterminer la catégorie du modèle sélectionné
        if (model.chat) {
          setOpenCategory('chat');
        } else if (model.webSearch) {
          setOpenCategory('web_search');
        } else if (model.reasoning) {
          setOpenCategory('reasoning');
        }
      }
    } else {
      setSelectedProvider("");
    }
  }, [selectedModel, models]);

  return {
    selectedModel,
    setSelectedModel,
    selectedProvider,
    openCategory,
    toggleCategory,
    models
  };
};
