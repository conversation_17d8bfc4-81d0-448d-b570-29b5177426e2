import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Id } from "../../convex/_generated/dataModel";
import { useChat } from "../components/features/chat/ComfyInterface/hooks/useChat";
import { useModelSelection, AUTOSELECT_ID } from "../components/features/chat/ComfyInterface/hooks/useModelSelection";
import ChatArea from "../components/features/chat/ComfyInterface/components/ChatArea";
import MessageInput from "../components/features/chat/ComfyInterface/components/MessageInput";

import logo from "../assets/logo3.svg";

/**
 * Page d'accueil améliorée adaptée pour fonctionner à l'intérieur du MainLayout
 * Contient uniquement le contenu principal, sans la sidebar
 */
const EnhancedHomePage: React.FC = () => {
  const navigate = useNavigate();

  // Fonction appelée lorsqu'une nouvelle conversation est créée
  const handleConversationCreated = (conversationId: Id<"conversations">) => {
    // Rediriger vers la page de conversation
    navigate(`/c/${conversationId}`);
  };

  // Utiliser les hooks personnalisés
  const {
    userMessage,
    setUserMessage,
    chatStarted,
    assistantResponse,
    isLoading,
    isStreaming,
    currentConversationId,
    activeButton,
    setActiveButton,
    bottomRef,
    conversations,
    handleSidebarClick,
    handleSelectConversation,
    handleNewConversation,
    handleKeyDown,
    startChat: originalStartChat,
    stopGeneration
  } = useChat(handleConversationCreated);

  // Utiliser le hook pour la sélection des modèles
  const {
    selectedModel,
    setSelectedModel,
    useAutoRouter,
    setUseAutoRouter,
    openCategory,
    toggleCategory,
    models,
    activeSelection,
    handleAutoSelect,
    handleModelSelect
  } = useModelSelection();

  // Fonction pour démarrer le chat avec le modèle sélectionné
  const startChat = (modelId?: string) => {
    const finalModelId = modelId || AUTOSELECT_ID;
    console.log("[EnhancedHomePage.tsx] startChat appelé avec modelId:", finalModelId);

    // Appeler la fonction originale avec le modèle sélectionné
    originalStartChat(finalModelId);
  };

  // Rendu de l'écran de bienvenue avec interface complète
  const renderWelcomeScreen = () => (
    <div className="flex-1 flex flex-col items-center justify-center p-4">
      <div className="flex justify-center mb-6">
        <img src={logo} alt="UnPourTous Logo" className="w-24 h-24" />
      </div>
      <p className="text-gray-400 text-center text-lg font-body mb-4">Je suis là pour discuter, aider ou simplement écouter</p>

      {/* Zone de saisie centrée avec interface complète */}
      <div className="w-full max-w-3xl mx-auto">
        <MessageInput
          userMessage={userMessage}
          setUserMessage={setUserMessage}
          isLoading={isLoading}
          handleKeyDown={handleKeyDown}
          startChat={startChat}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          useAutoRouter={useAutoRouter}
          setUseAutoRouter={setUseAutoRouter}
          onStop={stopGeneration}
        />
      </div>
    </div>
  );

  // Rendu de l'écran de chat
  const renderChatScreen = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      <div className="max-w-3xl mx-auto">
        {userMessage && (
          <div className="mb-6">
            <div className="bg-claude-light-gray/20 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
              <div className="text-white font-body">{userMessage || "Comment puis-je vous aider ?"}</div>
            </div>
          </div>
        )}
        {(assistantResponse || isLoading) && (
          <div>
            <div className="bg-claude-light-gray/45 rounded-xl p-4">
              <div className="font-medium mb-1 text-gray-400 font-body">Assistant IA</div>
              {isLoading ? (
                <div className="text-white font-body">
                  <span className="animate-pulse">...</span>
                </div>
              ) : (
                <div className="text-white font-body">{assistantResponse}</div>
              )}
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
    </div>
  );

  // Rendu du contenu principal uniquement, sans la sidebar
  // La sidebar est maintenant gérée par le MainLayout
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Chat Area */}
      <ChatArea
        chatStarted={chatStarted}
        conversationId={currentConversationId}
        userMessage={userMessage}
        assistantResponse={assistantResponse}
        isLoading={isLoading}
        renderWelcomeScreen={renderWelcomeScreen}
        renderChatScreen={renderChatScreen}
      />

      {/* Message Input pour les conversations en cours */}
      {chatStarted && (
        <div className="border-t border-claude-light-gray">
          <MessageInput
            userMessage={userMessage}
            setUserMessage={setUserMessage}
            isLoading={isLoading}
            handleKeyDown={handleKeyDown}
            startChat={startChat}
            selectedModel={selectedModel}
            setSelectedModel={setSelectedModel}
            useAutoRouter={useAutoRouter}
            setUseAutoRouter={setUseAutoRouter}
            onStop={stopGeneration}
          />
        </div>
      )}
    </div>
  );
};

export default EnhancedHomePage;
