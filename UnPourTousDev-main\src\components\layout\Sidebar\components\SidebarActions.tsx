import React from "react";
import { SidebarActionsProps } from "../types";

/**
 * Composant pour les actions de la sidebar (nouvelle conversation, etc.)
 */
const SidebarActions: React.FC<SidebarActionsProps> = ({ onNewConversation, isCollapsed }) => {
  return (
    <div className={`p-3 ${isCollapsed ? 'flex flex-col items-center' : ''}`}>
      <button
        onClick={onNewConversation}
        className={`${
          isCollapsed
            ? "p-2 rounded-full"
            : "w-full p-2 rounded-lg flex items-center"
        } bg-primary hover:bg-primary-hover text-white transition-colors`}
        title="Nouvelle conversation"
      >
        {isCollapsed ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="font-medium">Nouvelle conversation</span>
          </>
        )}
      </button>
    </div>
  );
};

export default SidebarActions;
