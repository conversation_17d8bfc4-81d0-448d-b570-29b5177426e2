import React from "react";
import { ChatAreaProps } from "../types";
import { useQuery } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import AiMessage from "../../AiMessage/AiMessage";
import UserMessage from "../../UserMessage/UserMessage";
import LoadingMessage from "../../../../LoadingMessage";
import { AUTOSELECT_ID } from "../hooks/useModelSelection";

/**
 * Composant ChatArea pour l'interface Comfy
 */
const ChatArea: React.FC<ChatAreaProps> = ({
  chatStarted,
  conversationId,
  userMessage,
  assistantResponse,
  isLoading,
  renderWelcomeScreen,
  renderChatScreen,
  selectedModel,
  activeSelection,
  models
}) => {
  // Récupérer les messages de la conversation depuis Convex
  const messages = useQuery(
    api.messages.list,
    conversationId ? { conversationId } : "skip"
  ) || [];

  // Déterminer le modèle et son nom pour l'animation de chargement
  const getLoadingModelInfo = () => {
    if (activeSelection === AUTOSELECT_ID) {
      return {
        modelId: "openrouter/auto",
        modelName: "AutoRouter"
      };
    }

    if (activeSelection && models) {
      const model = models.find((m: any) => m.modelId === activeSelection);
      return {
        modelId: activeSelection,
        modelName: model?.name || activeSelection
      };
    }

    return {
      modelId: undefined,
      modelName: undefined
    };
  };

  const { modelId: loadingModelId, modelName: loadingModelName } = getLoadingModelInfo();

  // Afficher l'écran de bienvenue si la conversation n'a pas commencé
  // OU si on est en train de créer une nouvelle conversation (chatStarted mais pas de conversationId)
  if (!chatStarted || (chatStarted && !conversationId)) {
    return renderWelcomeScreen();
  }

  // Si nous avons un ID de conversation, afficher les messages de la conversation
  if (conversationId && messages.length > 0) {
    return (
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {messages.map((message) => (
          <div key={message._id} className="max-w-4xl mx-auto">
            {message.role === "user" ? (
              <UserMessage message={message} />
            ) : (
              <AiMessage message={message} />
            )}
          </div>
        ))}
        {isLoading && (
          <div className="max-w-4xl mx-auto">
            <LoadingMessage
              modelName={loadingModelName}
              modelId={loadingModelId}
            />
          </div>
        )}
      </div>
    );
  }

  // Si on a un conversationId mais pas encore de messages, afficher l'écran de bienvenue
  // (évite l'affichage de l'interface temporaire indésirable)
  return renderWelcomeScreen();
};

export default ChatArea;
