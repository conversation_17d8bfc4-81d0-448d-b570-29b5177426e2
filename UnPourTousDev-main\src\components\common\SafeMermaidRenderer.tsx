import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

interface SafeMermaidRendererProps {
  content: string;
}

/**
 * Composant sécurisé pour le rendu des diagrammes Mermaid
 * Utilise une approche qui isole complètement les erreurs
 */
const SafeMermaidRenderer: React.FC<SafeMermaidRendererProps> = ({ content }) => {
  const [showCode, setShowCode] = useState(false);
  const [renderAttempted, setRenderAttempted] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour nettoyer le contenu du conteneur
  const cleanupContainer = () => {
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }
  };

  // Fonction pour rendre le diagramme de manière sécurisée
  const renderSafely = async () => {
    if (!containerRef.current) return;
    
    // Nettoyer le conteneur avant de tenter un nouveau rendu
    cleanupContainer();
    
    try {
      // Configuration minimale de Mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
        securityLevel: 'loose',
        logLevel: 1, // 1 = error (minimum)
        flowchart: { useMaxWidth: true },
        sequence: { useMaxWidth: true },
        gantt: { useMaxWidth: true }
      });
      
      // Vérifier si le diagramme est valide
      const isValid = await mermaid.parse(content);
      
      if (isValid) {
        // Créer un élément temporaire pour le rendu
        const tempContainer = document.createElement('div');
        tempContainer.style.maxHeight = '500px';
        tempContainer.style.overflow = 'hidden';
        
        // Générer un ID unique
        const id = `mermaid-${Date.now()}`;
        
        // Rendre dans le conteneur temporaire
        const { svg } = await mermaid.render(id, content);
        
        // Vérifier si le SVG est valide et ne contient pas d'erreurs
        if (svg && !svg.includes('error-icon') && !svg.includes('error-text')) {
          // Ajouter le SVG au conteneur réel
          containerRef.current.innerHTML = svg;
          
          // Appliquer des styles pour limiter la taille
          const svgElement = containerRef.current.querySelector('svg');
          if (svgElement) {
            svgElement.style.maxWidth = '100%';
            svgElement.style.maxHeight = '500px';
            svgElement.style.overflow = 'hidden';
          }
          
          setError(null);
        } else {
          throw new Error('Le SVG généré contient des erreurs');
        }
      } else {
        throw new Error('Diagramme invalide');
      }
    } catch (err) {
      console.error('Erreur Mermaid:', err);
      setError('Erreur de syntaxe dans le diagramme');
      
      // Assurer que le conteneur est vide en cas d'erreur
      cleanupContainer();
    } finally {
      setRenderAttempted(true);
    }
  };

  // Effectuer le rendu au chargement ou lorsque le contenu change
  useEffect(() => {
    if (!showCode) {
      renderSafely();
    }
  }, [content, showCode]);

  // Basculer entre l'affichage du code et du diagramme
  const toggleView = () => {
    setShowCode(!showCode);
  };

  return (
    <div className="relative border border-gray-300 dark:border-gray-700 rounded-lg overflow-hidden">
      {/* Bouton de basculement */}
      <button
        onClick={toggleView}
        className="absolute top-2 right-2 bg-gray-700 hover:bg-gray-600 text-white text-xs px-2 py-1 rounded z-10"
      >
        {showCode ? "Voir l'aperçu" : "Voir le code"}
      </button>

      {/* Affichage du code ou du diagramme */}
      {showCode ? (
        <div className="bg-gray-800 p-4 text-gray-200 font-mono text-sm whitespace-pre-wrap overflow-auto">
          {content}
        </div>
      ) : (
        <div className="p-4 overflow-hidden" style={{ maxHeight: '500px' }}>
          {/* Conteneur pour le rendu du diagramme */}
          <div 
            ref={containerRef} 
            className="safe-mermaid-container"
            style={{ maxHeight: '100%', overflow: 'hidden' }}
          />
          
          {/* Message d'erreur ou de chargement */}
          {renderAttempted && error && (
            <div className="text-center p-2 text-red-500 bg-red-50 dark:bg-red-900/20 rounded mt-2">
              {error}
            </div>
          )}
          
          {!renderAttempted && !error && (
            <div className="text-center p-2">
              Chargement du diagramme...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SafeMermaidRenderer;
