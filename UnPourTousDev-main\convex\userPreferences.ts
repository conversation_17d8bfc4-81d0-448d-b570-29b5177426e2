import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Récupère les préférences de l'utilisateur authentifié
 */
export const get = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const userPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .unique();

    return userPreferences || { userId: identity.subject };
  },
});

/**
 * Définit les préférences de l'utilisateur authentifié
 */
export const set = mutation({
  args: {
    interfaceType: v.optional(v.string()),
    theme: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    const userPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .unique();

    if (userPreferences) {
      // Mettre à jour les préférences existantes
      return await ctx.db.patch(userPreferences._id, {
        interfaceType: args.interfaceType,
        theme: args.theme,
      });
    } else {
      // Créer de nouvelles préférences
      return await ctx.db.insert("userPreferences", {
        userId: identity.subject,
        interfaceType: args.interfaceType,
        theme: args.theme,
      });
    }
  },
});

/**
 * Définit le type d'interface préféré de l'utilisateur
 */
export const setInterfaceType = mutation({
  args: {
    interfaceType: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    const userPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .unique();

    if (userPreferences) {
      // Mettre à jour le type d'interface
      return await ctx.db.patch(userPreferences._id, {
        interfaceType: args.interfaceType,
      });
    } else {
      // Créer de nouvelles préférences avec le type d'interface
      return await ctx.db.insert("userPreferences", {
        userId: identity.subject,
        interfaceType: args.interfaceType,
      });
    }
  },
});

/**
 * Définit le thème préféré de l'utilisateur
 */
export const setTheme = mutation({
  args: {
    theme: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Non authentifié");

    const userPreferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_user", (q) => q.eq("userId", identity.subject))
      .unique();

    if (userPreferences) {
      // Mettre à jour le thème
      return await ctx.db.patch(userPreferences._id, {
        theme: args.theme,
      });
    } else {
      // Créer de nouvelles préférences avec le thème
      return await ctx.db.insert("userPreferences", {
        userId: identity.subject,
        theme: args.theme,
      });
    }
  },
});
