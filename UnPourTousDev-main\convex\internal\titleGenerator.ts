import { internalMutation } from "../_generated/server";
import { v } from "convex/values";

/**
 * Met à jour le titre d'une conversation (pour usage interne)
 */
export const updateConversationTitle = internalMutation({
  args: {
    conversationId: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    // Met à jour le titre de la conversation sans vérifier l'authentification
    await ctx.db.patch(args.conversationId, { 
      title: args.title 
    });
    return { success: true };
  },
});
