import React from "react";
import { ModelCategoryProps } from "../types";
import { motion, AnimatePresence } from "framer-motion";
import { CategoryIcon } from "../../../../features/models";
import ModelButton from "./ModelButton";

/**
 * Composant pour une catégorie de modèles
 */
const ModelCategory: React.FC<ModelCategoryProps> = ({
  category,
  title,
  models,
  selectedModel,
  setSelectedModel,
  openCategory,
  toggleCategory
}) => {
  // Déterminer les classes CSS en fonction de la catégorie
  const getCategoryClasses = () => {
    switch (category) {
      case 'chat':
        return "bg-bronze-50 dark:bg-bronze-800 hover:bg-bronze-100 dark:hover:bg-bronze-700";
      case 'web_search':
        return "bg-tango-50 dark:bg-tango-900/30 hover:bg-tango-100 dark:hover:bg-tango-900/40";
      case 'reasoning':
        return "bg-fire-50 dark:bg-fire-900/30 hover:bg-fire-100 dark:hover:bg-fire-900/40";
      default:
        return "bg-bronze-50 dark:bg-bronze-800 hover:bg-bronze-100 dark:hover:bg-bronze-700";
    }
  };

  // Déterminer la couleur de l'icône en fonction de la catégorie
  const getIconColor = () => {
    switch (category) {
      case 'chat':
        return "text-bronze-700 dark:text-tango-300";
      case 'web_search':
        return "text-tango-600 dark:text-tango-400";
      case 'reasoning':
        return "text-fire-600 dark:text-fire-400";
      default:
        return "text-bronze-700 dark:text-tango-300";
    }
  };

  // Déterminer la couleur de la flèche en fonction de la catégorie
  const getArrowColor = () => {
    switch (category) {
      case 'chat':
        return "text-bronze-600 dark:text-tango-400";
      case 'web_search':
        return "text-tango-500 dark:text-tango-400";
      case 'reasoning':
        return "text-fire-500 dark:text-fire-400";
      default:
        return "text-bronze-600 dark:text-tango-400";
    }
  };

  return (
    <div className="rounded-lg overflow-hidden shadow-sm">
      <button
        type="button"
        onClick={(e) => {
          e.preventDefault(); // Empêcher la soumission du formulaire
          toggleCategory(category);
        }}
        className={`w-full flex items-center justify-between p-3 ${getCategoryClasses()} transition-colors`}
      >
        <div className="flex items-center">
          <CategoryIcon category={category} size={20} className={`${getIconColor()} mr-2`} />
          <h3 className="text-lg font-medium text-bronze-700 dark:text-tango-200">{title}</h3>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 ${getArrowColor()} transition-transform duration-200 ${openCategory === category ? 'rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      <AnimatePresence>
        {openCategory === category && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="flex flex-wrap gap-3 p-4">
              {models.map((model) => (
                <ModelButton
                  key={model._id}
                  model={model}
                  isSelected={selectedModel === model.modelId}
                  onClick={() => setSelectedModel(model.modelId)}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ModelCategory;
