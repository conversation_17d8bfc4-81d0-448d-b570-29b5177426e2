# Suivi de la Refactorisation de UnPourTous

Ce document suit l'avancement de la refactorisation du projet UnPourTous.

## Composants à Refactoriser

Voici la liste des composants volumineux qui nécessitent une refactorisation :

| Composant | Taille (lignes) | Statut | Complexité |
|-----------|-----------------|--------|------------|
| Sidebar.tsx | 853 | ✅ Terminé | Très élevée |
| NewConversationForm.tsx | 353 | ✅ Terminé | Moyenne |
| FolderItem.tsx | 315 | ✅ Terminé | Élevée |
| AiMessage.tsx | 188 | ✅ Terminé | Moyenne |
| SettingsPage.tsx | 171 | À faire | Moyenne |
| ChatArea.tsx | ~350 | À faire | Moyenne |
| MessageInput.tsx | ~250 | À faire | Moyenne |
| ModelSelector.tsx | ~150 | À faire | Moyenne |
| NewFolderButton.tsx | ~200 | À faire | Moyenne |
| ThemeColorTester.tsx | ~300 | À faire | Moyenne |

## Détails des Refactorisations

### 1. NewConversationForm (✅ Terminé)

**Date de refactorisation :** 09/05/2025

**Approche :** Décomposition en sous-composants et extraction de la logique dans des hooks personnalisés.

**Structure créée :**
```
src/components/features/chat/NewConversationForm/
├── components/
│   ├── MessageTextarea.tsx     # Zone de texte pour le message
│   ├── ModelButton.tsx         # Bouton pour un modèle individuel
│   ├── ModelCategory.tsx       # Section pour une catégorie de modèles
│   ├── ModelSelector.tsx       # Sélecteur de modèles
│   └── AutoRouterButton.tsx    # Bouton pour l'option AutoRouter
├── hooks/
│   ├── useModelSelection.ts    # Gestion de la sélection des modèles
│   └── useMessageInput.ts      # Gestion de la saisie et de l'envoi des messages
├── types.ts                    # Types pour le formulaire
├── utils.ts                    # Fonctions utilitaires
├── index.ts                    # Exports simplifiés
└── NewConversationForm.tsx     # Composant principal
```

**Améliorations apportées :**
- Séparation des responsabilités
- Extraction de la logique métier dans des hooks personnalisés
- Typage fort avec TypeScript
- Composants plus petits et plus maintenables
- Facilitation des imports avec un fichier index.ts

### 2. Sidebar (✅ Terminé)

**Date de refactorisation :** 09/05/2025

**Approche :** Décomposition en sous-composants et extraction de la logique dans des hooks personnalisés.

**Structure créée :**
```
src/components/layout/Sidebar/
├── components/
│   ├── SidebarHeader.tsx      # En-tête de la sidebar
│   ├── SidebarFooter.tsx      # Pied de page avec bouton de paramètres
│   ├── SidebarActions.tsx     # Boutons d'action (nouvelle conversation)
│   ├── ConversationList.tsx   # Liste des conversations
│   ├── ConversationItem.tsx   # Élément de conversation individuel
│   ├── FolderList.tsx         # Liste des dossiers
│   ├── FolderItem.tsx         # Élément de dossier individuel
│   └── ContextMenus.tsx       # Gestion des menus contextuels
├── hooks/
│   └── useSidebar.ts          # Gestion de l'état et de la logique
├── types.ts                   # Types pour la sidebar
├── utils.ts                   # Fonctions utilitaires
├── index.ts                   # Exports simplifiés
└── Sidebar.tsx                # Composant principal
```

**Améliorations apportées :**
- Séparation des responsabilités
- Extraction de la logique métier dans un hook personnalisé
- Typage fort avec TypeScript
- Composants plus petits et plus maintenables
- Facilitation des imports avec un fichier index.ts
- Meilleure gestion des menus contextuels

### 3. FolderItem (✅ Terminé)

**Date de refactorisation :** 09/05/2025

**Approche :** Décomposition en sous-composants et extraction de la logique dans des hooks personnalisés.

**Structure créée :**
```
src/components/features/folders/FolderItem/
├── components/
│   ├── FolderHeader.tsx           # En-tête du dossier
│   ├── FolderRenameForm.tsx       # Formulaire de renommage
│   ├── FolderDeleteConfirmation.tsx # Confirmation de suppression
│   ├── ConversationItem.tsx       # Élément de conversation individuel
│   └── ConversationsList.tsx      # Liste des conversations dans le dossier
├── hooks/
│   └── useFolder.ts               # Gestion de l'état et de la logique
├── types.ts                       # Types pour le dossier
├── index.ts                       # Exports simplifiés
└── FolderItem.tsx                 # Composant principal
```

**Améliorations apportées :**
- Séparation des responsabilités
- Extraction de la logique métier dans un hook personnalisé
- Typage fort avec TypeScript
- Composants plus petits et plus maintenables
- Facilitation des imports avec un fichier index.ts

### 4. AiMessage (✅ Terminé)

**Date de refactorisation :** 09/05/2025

**Approche :** Décomposition en sous-composants et extraction de la logique dans des hooks personnalisés.

**Structure créée :**
```
src/components/features/chat/AiMessage/
├── components/
│   ├── MessageHeader.tsx        # En-tête du message avec info sur le modèle
│   ├── MessageContent.tsx       # Contenu du message
│   ├── SourcesList.tsx          # Liste des sources/références
│   └── MarkdownRenderer.tsx     # Rendu du contenu Markdown
├── hooks/
│   ├── useAiMessage.ts          # Gestion de l'état et de la logique
│   └── useMarkdown.ts           # Formatage du contenu Markdown
├── types.ts                     # Types pour le message
├── index.ts                     # Exports simplifiés
└── AiMessage.tsx                # Composant principal
```

**Améliorations apportées :**
- Séparation des responsabilités
- Extraction de la logique métier dans des hooks personnalisés
- Typage fort avec TypeScript
- Composants plus petits et plus maintenables
- Facilitation des imports avec un fichier index.ts

## Prochaines Refactorisations Prévues

### 5. SettingsPage (Priorité Moyenne)

**Plan de refactorisation :**
- Créer un dossier dédié `src/components/features/settings/SettingsPage/`
- Diviser en sous-composants (SettingsHeader, ThemeSettings, ApiKeySettings, etc.)
- Extraire la logique dans des hooks personnalisés
- Ajouter des types forts

## Problèmes Connus

- Les imports dans les fichiers existants doivent être mis à jour pour refléter la nouvelle structure
- Des erreurs TypeScript apparaissent en raison des chemins d'importation non mis à jour
- Les alias de chemin doivent être configurés dans tsconfig.json pour simplifier les imports
