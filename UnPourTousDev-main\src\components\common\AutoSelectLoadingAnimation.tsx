import React from 'react';
import { motion } from 'framer-motion';

interface AutoSelectLoadingAnimationProps {
  isLoading: boolean;
  logoSrc: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Animation de chargement AutoSelect avec logo qui sautille
 * Selon la documentation : "AutoSelect : Logo qui sautille pendant la sélection"
 */
const AutoSelectLoadingAnimation: React.FC<AutoSelectLoadingAnimationProps> = ({
  isLoading,
  logoSrc,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const bounceAnimation = {
    y: [0, -8, 0],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.img
        src={logoSrc}
        alt="AutoSelect"
        className={`${sizeClasses[size]} object-contain`}
        animate={isLoading ? bounceAnimation : {}}
        style={{
          filter: isLoading ? 'brightness(1.1)' : 'brightness(1)'
        }}
      />
    </div>
  );
};

export default AutoSelectLoadingAnimation;
