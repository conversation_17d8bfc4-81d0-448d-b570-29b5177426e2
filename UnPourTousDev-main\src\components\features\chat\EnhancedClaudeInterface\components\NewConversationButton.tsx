import React from 'react';
import logo from "../../../../../assets/logo3.svg";

interface NewConversationButtonProps {
  onNewConversation: () => void;
  buttonClass: string;
  visualState: boolean;
}

const NewConversationButton: React.FC<NewConversationButtonProps> = ({
  onNewConversation,
  buttonClass,
  visualState
}) => {
  return (
    <button
      className={`${buttonClass} bg-claude-orange hover:bg-claude-orange/90 text-white`}
      onClick={onNewConversation}
      title="Nouvelle conversation"
    >
      {visualState ? (
        <div className="flex items-center justify-center w-full h-full">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 flex-shrink-0">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            <span className="ml-2 text-sm font-medium">Nouvelle conversation</span>
          </div>
        </div>
      )}
    </button>
  );
};

export default NewConversationButton;
