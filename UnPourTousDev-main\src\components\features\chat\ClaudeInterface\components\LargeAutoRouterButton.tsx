import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import logo from "../../../../../assets/logo3.svg";
import logoSleep from "../../../../../assets/logosleep.svg";
import gradientBg from "../../../../../assets/appleintelligencegradient.png";
import { LargeAutoRouterButtonProps } from "../types";
import { AUTOSELECT_ID } from "../hooks/useModelSelection";

// Styles pour les effets visuels
const customStyles = `
  .text-shadow {
    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
  }

  .gradient-glow {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  }
`;

/**
 * Composant LargeAutoRouterButton pour l'interface Claude
 * Affiche un bouton AutoRouter plus grand et plus visible avec un fond de gradient statique lorsqu'il est activé
 * Les animations se produisent uniquement lors des transitions entre les états activé et désactivé
 */
const LargeAutoRouterButton: React.FC<LargeAutoRouterButtonProps> = ({
  activeSelection,
  handleAutoSelect
}) => {
  // Déterminer si AutoSelect est actif
  const isAutoSelectActive = activeSelection === AUTOSELECT_ID;
  return (
    <>
      {/* Injecter les styles CSS personnalisés */}
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />

      <motion.button
      type="button"
      className={`w-full py-3 px-4 rounded-lg relative overflow-hidden ${
        isAutoSelectActive
          ? "text-white gradient-glow"
          : "bg-claude-gray text-white border border-claude-light-gray hover:bg-claude-light-gray/30"
      }`}
      style={{
        transition: "background-color 0.4s ease-out, box-shadow 0.4s ease-out"
      }}
      onClick={handleAutoSelect}
      title={isAutoSelectActive ? "Désactiver AutoSelect" : "Activer AutoSelect"}
      animate={{
        scale: isAutoSelectActive ? 1.02 : 1,
        transition: { duration: 0.3 }
      }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 }
      }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Arrière-plan avec gradient et transition d'état */}
      <AnimatePresence>
        {isAutoSelectActive && (
          <motion.div
            className="absolute inset-0 z-0 overflow-hidden"
            initial={{ opacity: 0, scale: 1.20 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 1 }}
            transition={{
              opacity: { duration: 0.4, ease: "easeOut" },
              scale: { duration: 0.5, ease: "easeInOut" }
            }}
          >
            {/* Gradient statique */}
            <div
              className="absolute inset-0 w-full h-full"
              style={{
                backgroundImage: `url(${gradientBg})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            />

            {/* Overlay semi-transparent pour améliorer le contraste et créer un effet de bordure subtile */}
            <div className="absolute inset-0 bg-black bg-opacity-15 rounded-lg"></div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Structure à trois colonnes avec espacement réduit */}
      <div className={`grid grid-cols-3 w-full items-center mx-auto max-w-md relative z-10 ${
        isAutoSelectActive ? "text-shadow" : ""
      }`}>
        {/* Colonne de gauche - Logo */}
        <div className="flex justify-end pr-1">
          <motion.div
            initial={false}
            animate={{ rotate: isAutoSelectActive ? 360 : 0 }}
            transition={{ duration: 0.7, ease: "easeInOut" }}
          >
            <motion.img
              key={isAutoSelectActive ? "active" : "inactive"}
              src={isAutoSelectActive ? logo : logoSleep}
              alt={isAutoSelectActive ? "UnPourTous Logo" : "UnPourTous Logo Sleep"}
              className="w-7 h-7"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>
        </div>

        {/* Colonne centrale - Texte (toujours centré) */}
        <div className="flex justify-center px-1">
          <AnimatePresence mode="wait">
            <motion.span
              key={isAutoSelectActive ? "active" : "inactive"}
              className="font-medium text-lg whitespace-nowrap"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              {isAutoSelectActive ? "AutoSelect" : "AutoSelect"}
            </motion.span>
          </AnimatePresence>
        </div>

        {/* Colonne de droite - Icône de validation (espace toujours réservé) */}
        <div className="flex justify-start pl-1">
          <div className="w-5 h-5 relative">
            <AnimatePresence>
              {isAutoSelectActive && (
                <motion.svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-white absolute top-0 left-0 drop-shadow-md"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.5 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </motion.svg>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.button>
    </>
  );
};

export default LargeAutoRouterButton;
