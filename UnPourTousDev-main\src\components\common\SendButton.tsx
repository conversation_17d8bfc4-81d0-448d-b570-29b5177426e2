import React, { useState } from 'react';

interface SendButtonProps {
  disabled?: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  isLoading?: boolean;
  onStop?: () => void;
}

const SendButton: React.FC<SendButtonProps> = ({
  disabled = false,
  onClick = () => {},
  size = 'md',
  className = '',
  children,
  type = 'button',
  isLoading = false,
  onStop
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // <PERSON><PERSON><PERSON> le clic selon l'état (Envoyer ou Stop)
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isLoading && onStop) {
      onStop();
    } else {
      onClick(e);
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      className={`
        ${sizeClasses[size]}
        relative
        rounded-xl
        border-0
        outline-none
        font-medium
        transition-all
        duration-200
        ease-out
        flex
        items-center
        justify-center
        ${disabled
          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
          : isLoading
          ? 'bg-red-500 hover:bg-red-600 text-white cursor-pointer hover:scale-105 active:scale-95'
          : `
            bg-gradient-to-br from-orange-500 to-orange-600
            text-white
            cursor-pointer
            hover:from-orange-400 hover:to-orange-500
            hover:shadow-lg hover:shadow-orange-500/40
            hover:scale-105
            active:scale-95
            focus:ring-2 focus:ring-orange-500/30 focus:ring-offset-2 focus:ring-offset-gray-900
          `
        }
        ${isPressed ? 'scale-95' : ''}
        ${className}
      `}
    >
      {children || (
        isLoading ? (
          // Icône Stop (carré) pendant le chargement
          <svg
            className={`${iconSizes[size]} transition-transform duration-200`}
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z" />
          </svg>
        ) : (
          // Icône Envoyer (flèche) par défaut
          <svg
            className={`${iconSizes[size]} transition-transform duration-200 ${disabled ? '' : 'hover:translate-x-0.5'}`}
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" />
          </svg>
        )
      )}
    </button>
  );
};

export default SendButton;
