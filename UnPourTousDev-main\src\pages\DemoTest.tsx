import React from 'react';
import MarkdownRenderer from '../components/features/chat/AiMessage/components/MarkdownRenderer';
import DemoNavigation from '../components/common/DemoNavigation';

/**
 * Composant de test simple pour vérifier le rendu des tableaux Markdown
 */
const TableTestSimple: React.FC = () => {
  // Exemple de contenu Markdown avec un tableau simple
  const tableContent = `
# Test de tableau simple

Voici un tableau de test simple :

| Catégorie | Élément | Statut    | Priorité |
| :-------- | :------ | :-------- | :------- |
| Tâche A   | Design  | En cours  | Haute    |
| Tâche B   | Dev     | À faire   | Moyenne  |
| Tâche C   | Test    | Terminé   | Basse    |
| Tâche D   | Doc     | En attente| Moyenne  |
`;

  return (
    <div className="min-h-screen bg-surface-light dark:bg-surface-dark">
      <DemoNavigation />
      <div className="p-6 pt-16 max-w-4xl mx-auto">
        <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Test de tableau simple</h2>
          <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg">
            <MarkdownRenderer
              content={tableContent}
              isStreaming={false}
              showCursor={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableTestSimple;
