import { Id } from "../../../../../convex/_generated/dataModel";

/**
 * Interface pour les propriétés du composant EnhancedClaudeInterface
 */
export interface EnhancedClaudeInterfaceProps {
  onConversationCreated?: (id: Id<"conversations">) => void;
}

/**
 * Interface pour les propriétés du composant EnhancedSidebar
 */
export interface EnhancedSidebarProps {
  activeButton: string;
  handleSidebarClick: (buttonType: string) => void;
  conversations: any[];
  currentConversationId: Id<"conversations"> | null;
  onSelectConversation: (id: Id<"conversations">) => void;
  onNewConversation: () => void;
  isCollapsed: boolean;
  toggleSidebar: () => void;
  navigateToSettings: () => void;
  hasMoreConversations?: boolean;
  isLoadingMore?: boolean;
  handleShowMore?: () => void;
  handleScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
  conversationListRef?: React.RefObject<HTMLDivElement>;

  // Propriétés pour les menus contextuels
  menuOpenId?: Id<"conversations"> | null;
  isRenaming?: Id<"conversations"> | null;
  newTitle?: string;
  confirmDelete?: Id<"conversations"> | null;
  settingsMenuOpen?: boolean;
  renameInputRef?: React.RefObject<HTMLInputElement>;

  // Fonctions pour les menus contextuels
  handleOpenConversationMenu?: (e: React.MouseEvent, conversationId: Id<"conversations">) => void;
  handleCloseConversationMenu?: () => void;
  toggleSettingsMenu?: () => void;
  handleCloseSettingsMenu?: () => void;
  handleStartRename?: (conversationId: Id<"conversations">, title: string) => void;
  handleRename?: (e: React.FormEvent, conversationId: Id<"conversations">) => void;
  setNewTitle?: (title: string) => void;
  handleCancelRename?: () => void;
  handleConfirmDelete?: (conversationId: Id<"conversations">) => void;
  handleDelete?: (conversationId: Id<"conversations">) => void;
  handleCancelDelete?: () => void;
  handleSignOut?: () => void;
}
