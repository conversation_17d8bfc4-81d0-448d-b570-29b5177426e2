import React from "react";
import ModelIcon from "./ModelIcon";

interface ModelIconDisplayProps {
  conversation: {
    usesAutoRouter?: boolean;
    lastModelUsed?: string;
    lastModelProvider?: string;
    title?: string;
    _id?: any;
  };
  size?: number;
  className?: string;
}

/**
 * Composant partagé pour afficher l'icône du modèle d'une conversation
 * Ce composant centralise la logique d'affichage des icônes pour les conversations
 * et gère correctement le cas AutoRouter
 */
const ModelIconDisplay: React.FC<ModelIconDisplayProps> = ({
  conversation,
  size = 16,
  className = ""
}) => {
  // Vérifier si c'est une conversation AutoRouter
  // 1. Si usesAutoRouter est explicitement true
  // 2. Si le titre contient "AutoRouter" (pour les anciennes conversations)
  // 3. Si le dernier modèle utilisé est "openrouter/auto"
  const isAutoRouter =
    conversation.usesAutoRouter === true ||
    (conversation.title && conversation.title.includes("AutoRouter")) ||
    conversation.lastModelUsed === "openrouter/auto";

  if (isAutoRouter) {
    return (
      <ModelIcon
        model="openrouter/auto"
        provider="openrouter"
        size={size}
        className={className}
      />
    );
  }

  // Pour les autres cas, utiliser le modèle et le fournisseur normaux
  const modelId = conversation.lastModelUsed || "openrouter/auto";
  const provider = conversation.lastModelProvider || "openrouter";



  return (
    <ModelIcon
      model={modelId}
      provider={provider}
      size={size}
      className={className}
    />
  );
};

export default ModelIconDisplay;
