import React from "react";
import { SidebarProps } from "./types";
import { useSidebar } from "./hooks/useSidebar";
import SidebarHeader from "./components/SidebarHeader";
import SidebarFooter from "./components/SidebarFooter";
import SidebarActions from "./components/SidebarActions";
import ConversationList from "./components/ConversationList";
import ContextMenus from "./components/ContextMenus";

/**
 * Composant Sidebar principal
 */
const Sidebar: React.FC<SidebarProps> = ({
  currentConversationId,
  onSelectConversation,
  onNewConversation
}) => {
  // Utiliser le hook personnalisé pour gérer l'état et la logique
  const {
    // États
    conversations,
    hasMoreConversations,
    menuOpen,
    isRenaming,
    newTitle,
    confirmDelete,
    menuPosition,
    isCollapsed,
    settingsMenuOpen,
    renameInputRef,
    conversationListRef,
    isLoadingMore,

    // Setters
    setNewTitle,
    setConfirmDelete,
    setMenuOpen,
    setSettingsMenuOpen,

    // Handlers
    handleSignOut,
    handleOpenMenu,
    handleStartRename,
    handleRename,
    handleConfirmDelete,
    handleDelete,
    handleOpenSettingsMenu,
    handleShowMore,
    handleScroll,
    toggleSidebar
  } = useSidebar(currentConversationId, onSelectConversation, onNewConversation);

  return (
    <div
      className={`flex flex-col h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-64"
      }`}
    >
      {/* En-tête de la sidebar */}
      <SidebarHeader
        isCollapsed={isCollapsed}
        toggleSidebar={toggleSidebar}
      />

      {/* Bouton de nouvelle conversation */}
      <SidebarActions
        onNewConversation={onNewConversation}
        isCollapsed={isCollapsed}
      />

      {/* Liste des conversations */}
      <div className="flex-1 overflow-hidden">
        <ConversationList
          conversations={conversations}
          currentConversationId={currentConversationId}
          onSelectConversation={onSelectConversation}
          onOpenMenu={handleOpenMenu}
          isCollapsed={isCollapsed}
          menuOpenId={menuOpen}
          isRenaming={isRenaming}
          newTitle={newTitle}
          setNewTitle={setNewTitle}
          handleRename={handleRename}
          confirmDelete={confirmDelete}
          handleDelete={handleDelete}
          setConfirmDelete={setConfirmDelete}
          renameInputRef={renameInputRef}
          hasMoreConversations={hasMoreConversations}
          isLoadingMore={isLoadingMore}
          onShowMore={handleShowMore}
          onScroll={handleScroll}
          conversationListRef={conversationListRef}
        />
      </div>

      {/* Pied de page avec bouton de paramètres */}
      <SidebarFooter
        isCollapsed={isCollapsed}
        onOpenSettingsMenu={handleOpenSettingsMenu}
        settingsMenuOpen={settingsMenuOpen}
      />

      {/* Menus contextuels */}
      <ContextMenus
        menuOpen={menuOpen}
        settingsMenuOpen={settingsMenuOpen}
        menuPosition={menuPosition}
        handleStartRename={handleStartRename}
        handleConfirmDelete={handleConfirmDelete}
        conversations={conversations}
        handleSignOut={handleSignOut}
        onCloseMenu={() => setMenuOpen(null)}
        onCloseSettingsMenu={() => setSettingsMenuOpen(false)}
      />
    </div>
  );
};

export default Sidebar;


