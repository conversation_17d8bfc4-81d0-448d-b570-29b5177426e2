import pygame
import os
import math
import random

# --- Constantes de Simulation et Vidéo ---
FPS = 60
DURATION_SECONDS = 45 # Augmenté pour voir l'effet avec plus d'arcs
TOTAL_FRAMES = FPS * DURATION_SECONDS

# --- Constantes d'Affichage ---
SCREEN_WIDTH = 1080
SCREEN_HEIGHT = 1920
CENTER_X, CENTER_Y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2

# Couleurs
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BALL_COLOR = (255, 0, 255)
ARC_COLOR = WHITE

# --- Paramètres des Arcs ---
TOTAL_POTENTIAL_ARCS = 1000     # Grand nombre d'arcs
BASE_ARC_RADIUS = 20            # Rayon MOYEN du tout premier arc (peut être très petit)
ARC_SPACING = 30                # Espacement entre les rayons MOYENS (plus serré pour 1000 arcs)
ARC_THICKNESS = 8               # Épaisseur du matériau de l'arc
ARC_GAP_DEGREES = 90            # Ouverture plus large pour faciliter avec beaucoup d'arcs
INITIAL_ARC_ROTATION_DEG = 90
ARC_ROTATION_SPEED_DEG = 0.8

# --- Limites de Visibilité/Activité des Arcs ---
MIN_ACTIVE_ARC_RADIUS = 50 # Rayon moyen minimum pour qu'un arc soit dessiné/interactif
MAX_ACTIVE_ARC_RADIUS = SCREEN_WIDTH * 0.7 # Rayon moyen maximum

# --- Paramètres de la Balle (vos valeurs) ---
BALL_RADIUS = 18
BALL_GRAVITY = 0.35
BALL_INITIAL_VEL_Y_RANGE = (-10, -7)
BALL_INITIAL_VEL_X_RANGE = (-2, 2)
BALL_RESTITUTION = 0.99

# --- Paramètres de Rétrécissement ---
RADIUS_SHRINK_ON_BREAK = ARC_SPACING # Quand un arc casse, les autres rétrécissent de cette valeur

# --- Classe ArcObstacle --- (inchangée par rapport à la version précédente)
class ArcObstacle:
    def __init__(self, radius, thickness, gap_angle_deg, initial_rotation_deg):
        self.center = pygame.math.Vector2(CENTER_X, CENTER_Y)
        self.radius = radius 
        self.thickness = thickness
        self.gap_angle_deg = gap_angle_deg
        self.current_rotation_deg = initial_rotation_deg
        self.is_broken = False
        self.outer_radius_material = self.radius + self.thickness / 2
        self.inner_radius_material = self.radius - self.thickness / 2

    def update_rotation(self, shared_rotation_speed):
        if not self.is_broken:
            self.current_rotation_deg = (self.current_rotation_deg + shared_rotation_speed) % 360
    
    def shrink_radius(self, amount):
        if not self.is_broken:
            self.radius -= amount
            if self.radius < 0: self.radius = 0 # Empêcher rayon négatif
            # Recalculer les bords du matériau après rétrécissement
            self.outer_radius_material = self.radius + self.thickness / 2
            self.inner_radius_material = self.radius - self.thickness / 2


    def draw(self, surface):
        if self.is_broken or self.radius <= 0: return
        start_angle_solid_rad = math.radians(self.current_rotation_deg + self.gap_angle_deg)
        stop_angle_solid_rad = math.radians(self.current_rotation_deg + 360)
        rect_radius_for_draw = self.radius + self.thickness / 2 
        arc_rect = pygame.Rect(
            self.center.x - rect_radius_for_draw, self.center.y - rect_radius_for_draw,
            2 * rect_radius_for_draw, 2 * rect_radius_for_draw
        )
        draw_thickness = max(1, int(self.thickness))
        try: pygame.draw.arc(surface, ARC_COLOR, arc_rect, start_angle_solid_rad, stop_angle_solid_rad, draw_thickness)
        except ValueError: pass
    def break_arc(self): self.is_broken = True

# --- Classe Ball --- (inchangée par rapport à la version précédente)
class Ball:
    def __init__(self, x, y, radius, color, gravity, restitution):
        self.pos = pygame.math.Vector2(x, y)
        vx = random.uniform(BALL_INITIAL_VEL_X_RANGE[0], BALL_INITIAL_VEL_X_RANGE[1])
        vy = random.uniform(BALL_INITIAL_VEL_Y_RANGE[0], BALL_INITIAL_VEL_Y_RANGE[1])
        self.vel = pygame.math.Vector2(vx, vy); self.radius = radius; self.color = color
        self.gravity = gravity; self.restitution = restitution
    def update(self): self.vel.y += self.gravity; self.pos += self.vel
    def check_collision_and_interact(self, active_arcs_to_check_list, frame_num_debug=-1):
        # active_arcs_to_check_list est déjà filtrée pour les arcs visibles/actifs
        active_arc_for_interaction = None
        dist_to_center_of_potential_arc = float('inf')

        # 1. Identifier l'arc le plus pertinent DANS LA LISTE FILTRÉE
        #   (celui dont le rayon moyen est le plus proche de la distance de la balle,
        #    ou simplement le premier qui pourrait être touché)
        # Pour cette version, on prend le premier de la liste filtrée qui est pertinent.
        # La liste filtrée devrait déjà être plus ou moins dans l'ordre des rayons.
        
        # On cherche le premier arc de la liste filtrée que la balle pourrait toucher
        # en étant DANS sa "bande d'influence" radiale
        for arc in active_arcs_to_check_list: # arcs_list est maintenant la liste filtrée
            if arc.is_broken: continue # Double vérification, devrait déjà être filtré

            current_dist_to_center = (self.pos - arc.center).length()
            
            # Si la balle est à l'intérieur du rayon extérieur du matériau de cet arc
            if current_dist_to_center < arc.outer_radius_material + self.radius:
                active_arc_for_interaction = arc
                dist_to_center_of_potential_arc = current_dist_to_center
                break # On a trouvé le premier arc pertinent dans la liste filtrée

        if not active_arc_for_interaction: return None
        
        arc = active_arc_for_interaction # Renommer pour la suite
        dist_ball_to_arc_center = dist_to_center_of_potential_arc

        # 2. Collision avec le "mur intérieur" du matériau de l'arc actif
        collision_boundary_for_ball_center = arc.inner_radius_material - self.radius
        vec_arc_center_to_ball = self.pos - arc.center
        if vec_arc_center_to_ball.length_squared() == 0: normal_n = pygame.math.Vector2(0, -1)
        else: normal_n = vec_arc_center_to_ball.normalize()

        if dist_ball_to_arc_center >= collision_boundary_for_ball_center and \
           self.vel.dot(normal_n) > 0:
            if vec_arc_center_to_ball.x == 0 and vec_arc_center_to_ball.y == 0: ball_angle_deg = 0
            else:
                ball_angle_rad = math.atan2(-vec_arc_center_to_ball.y, vec_arc_center_to_ball.x)
                ball_angle_deg = math.degrees(ball_angle_rad)
            ball_angle_deg = (ball_angle_deg + 360) % 360
            gap_start_deg = arc.current_rotation_deg % 360
            gap_end_deg = (arc.current_rotation_deg + arc.gap_angle_deg) % 360
            is_in_gap = False
            if gap_start_deg < gap_end_deg:
                if gap_start_deg < ball_angle_deg < gap_end_deg: is_in_gap = True
            else:
                if ball_angle_deg > gap_start_deg or ball_angle_deg < gap_end_deg: is_in_gap = True
            if arc.gap_angle_deg >= 359: is_in_gap = True
            if is_in_gap:
                if arc.inner_radius_material < dist_ball_to_arc_center < arc.outer_radius_material: # S'assurer qu'elle traverse le matériau
                    arc.break_arc()
                    return "BROKEN"
            else: # Rebond
                Ve_proj_N_vec = normal_n * self.vel.dot(normal_n)
                self.vel = self.vel - 2 * Ve_proj_N_vec
                self.vel *= self.restitution
                epsilon = 1.5
                self.pos = arc.center + normal_n * (collision_boundary_for_ball_center - epsilon)
                return "BOUNCED"
        return None
    def draw(self, surface):pygame.draw.circle(surface, self.color, (int(self.pos.x), int(self.pos.y)), self.radius)

# --- Initialisation ---
pygame.init()
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("1000 Arcs Filtrés")
clock = pygame.time.Clock()
FRAMES_DIR = "frames_1000_arcs_filtered"
if not os.path.exists(FRAMES_DIR): os.makedirs(FRAMES_DIR)

# --- Création de TOUS les Arcs Potentiels ---
all_arcs_list = [] # Liste contenant les 1000 arcs
INITIAL_ROTATION_OFFSET_PER_ARC_DEG = 360 / TOTAL_POTENTIAL_ARCS
for i in range(TOTAL_POTENTIAL_ARCS):
    radius = BASE_ARC_RADIUS + i * ARC_SPACING 
    arc_initial_rot = (INITIAL_ARC_ROTATION_DEG + i * INITIAL_ROTATION_OFFSET_PER_ARC_DEG) % 360
    all_arcs_list.append(ArcObstacle(
        radius=radius, thickness=ARC_THICKNESS,
        gap_angle_deg=ARC_GAP_DEGREES, initial_rotation_deg=arc_initial_rot ))
print(f"{len(all_arcs_list)} arcs potentiels créés.")

# --- Création de la Balle ---
ball = Ball(CENTER_X, CENTER_Y, BALL_RADIUS, BALL_COLOR, BALL_GRAVITY, BALL_RESTITUTION)

# --- Boucle Principale ---
print(f"Début de la génération de {TOTAL_FRAMES} images...")
for frame_num in range(TOTAL_FRAMES):
    for event in pygame.event.get():
        if event.type == pygame.QUIT: TOTAL_FRAMES=frame_num; frame_num=TOTAL_FRAMES-1; break
    if frame_num >= TOTAL_FRAMES: break

    # Mettre à jour la rotation de TOUS les arcs
    for arc in all_arcs_list:
        arc.update_rotation(ARC_ROTATION_SPEED_DEG)
    
    ball.update()

    # Filtrer les arcs actifs pour cette frame (pour collision et dessin)
    # Un arc est actif s'il n'est pas cassé ET si son rayon moyen est dans la plage visible.
    active_arcs_for_frame = []
    for arc in all_arcs_list:
        if not arc.is_broken and \
           MIN_ACTIVE_ARC_RADIUS <= arc.radius <= MAX_ACTIVE_ARC_RADIUS:
            active_arcs_for_frame.append(arc)
    
    # Trier les arcs actifs par rayon (le plus petit en premier) est implicite si all_arcs_list l'est.
    # Mais pour être sûr, surtout si la liste est modifiée dynamiquement (non le cas ici) :
    # active_arcs_for_frame.sort(key=lambda arc: arc.radius) # Généralement pas nécessaire si all_arcs_list est déjà ordonnée

    interaction = ball.check_collision_and_interact(active_arcs_for_frame, frame_num)

    if interaction == "BROKEN":
        # Si un arc est cassé, tous les arcs (dans la liste originale) rétrécissent
        for arc_to_shrink in all_arcs_list:
            if not arc_to_shrink.is_broken: # Ne pas rétrécir ceux déjà cassés
                arc_to_shrink.shrink_radius(RADIUS_SHRINK_ON_BREAK)
                # Si un arc rétrécit en dessous de la base, il pourrait être "réinitialisé"
                # ou marqué pour ne plus être utilisé, mais pour l'instant, laissons-le juste rétrécir.
                # S'il rétrécit trop, il sortira de la plage MIN_ACTIVE_ARC_RADIUS.

    # Dessiner
    screen.fill(BLACK)
    # Ne dessiner que les arcs actifs (déjà filtrés, mais la méthode draw a aussi son propre filtre)
    for arc in active_arcs_for_frame: 
        arc.draw(screen)
    ball.draw(screen)
    pygame.display.flip()

    # Sauvegarder
    frame_filename = os.path.join(FRAMES_DIR, f"frame_{frame_num:05d}.png")
    pygame.image.save(screen, frame_filename)

    if (frame_num + 1) % 60 == 0: print(f"Image {frame_num + 1}/{TOTAL_FRAMES} générée...")
    # clock.tick(FPS)

pygame.quit()
print(f"Terminé ! {TOTAL_FRAMES} images sauvegardées dans '{FRAMES_DIR}'.")