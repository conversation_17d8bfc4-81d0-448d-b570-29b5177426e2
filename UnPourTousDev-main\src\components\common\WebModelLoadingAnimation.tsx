import React from 'react';
import { motion } from 'framer-motion';

interface WebModelLoadingAnimationProps {
  isLoading: boolean;
  modelName: string;
  modelIcon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Animation de chargement pour les modèles de recherche web
 * Selon la documentation : "Recherche web : Chargement jusqu'à affichage complet (non streamé)"
 */
const WebModelLoadingAnimation: React.FC<WebModelLoadingAnimationProps> = ({
  isLoading,
  modelName,
  modelIcon,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const spinAnimation = {
    rotate: 360,
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "linear"
    }
  };

  const searchAnimation = {
    scale: [1, 1.2, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Icône du modèle avec animation de recherche */}
      <motion.div
        className={`${sizeClasses[size]} flex items-center justify-center relative`}
        animate={isLoading ? searchAnimation : {}}
      >
        {modelIcon || (
          <div className="w-full h-full bg-green-600 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
            </svg>
          </div>
        )}
        
        {/* Cercle de chargement autour de l'icône */}
        {isLoading && (
          <motion.div
            className="absolute inset-0 border-2 border-green-500 border-t-transparent rounded-full"
            animate={spinAnimation}
          />
        )}
      </motion.div>

      {/* Nom du modèle avec indicateur de recherche */}
      {isLoading && (
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-300">
            {modelName}
          </span>
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1 h-1 bg-green-500 rounded-full"
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WebModelLoadingAnimation;
