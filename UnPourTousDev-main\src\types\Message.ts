import { Id } from "../../convex/_generated/dataModel";

/**
 * Type représentant un message dans l'application
 */
export interface Message {
  _id: Id<"messages">;
  _creationTime: number;
  conversationId: Id<"conversations">;
  role: "user" | "assistant";
  content: string;
  modelUsed?: string;
}

/**
 * Type pour la création d'un nouveau message
 */
export type NewMessage = {
  conversationId: Id<"conversations">;
  role: "user" | "assistant";
  content: string;
  modelUsed?: string;
};
