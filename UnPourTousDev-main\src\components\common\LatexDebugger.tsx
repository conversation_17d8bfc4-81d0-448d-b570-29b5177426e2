import React from 'react';

interface LatexDebuggerProps {
  content: string;
}

/**
 * Composant de débogage pour identifier les formules LaTeX dans le contenu
 */
const LatexDebugger: React.FC<LatexDebuggerProps> = ({ content }) => {
  // Extraire toutes les formules LaTeX du contenu
  const extractFormulas = (text: string) => {
    const patterns = [
      { regex: /\$\$(.*?)\$\$/gs, type: 'Block $$' },
      { regex: /\$(.*?)\$/g, type: 'Inline $' },
      { regex: /\\\[(.*?)\\\]/gs, type: 'Block \\[' },
      { regex: /\\\((.*?)\\\)/g, type: 'Inline \\(' }
    ];
    
    const formulas: Array<{type: string, formula: string}> = [];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.regex.exec(text)) !== null) {
        formulas.push({
          type: pattern.type,
          formula: match[1]
        });
      }
    });
    
    return formulas;
  };
  
  const formulas = extractFormulas(content);
  
  if (formulas.length === 0) {
    return <div className="p-2 text-gray-500">Aucune formule LaTeX détectée</div>;
  }
  
  return (
    <div className="p-2 border rounded bg-gray-100 dark:bg-gray-800 text-sm">
      <h3 className="font-bold mb-2">Formules LaTeX détectées ({formulas.length})</h3>
      <ul className="space-y-2">
        {formulas.map((item, index) => (
          <li key={index} className="border-b pb-1">
            <div><span className="font-semibold">Type:</span> {item.type}</div>
            <div><span className="font-semibold">Formule:</span> <code>{item.formula}</code></div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LatexDebugger;
