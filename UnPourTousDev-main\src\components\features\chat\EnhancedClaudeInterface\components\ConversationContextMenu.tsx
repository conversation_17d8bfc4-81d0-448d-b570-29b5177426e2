import React, { useEffect, useState } from 'react';
import { Id } from '../../../../../../convex/_generated/dataModel';

interface ConversationContextMenuProps {
  conversationId: Id<"conversations">;
  title: string;
  isOpen: boolean;
  onClose: () => void;
  onRename: (conversationId: Id<"conversations">, title: string) => void;
  onDelete: (conversationId: Id<"conversations">) => void;
  triggerRef: React.RefObject<HTMLButtonElement | null>;
}

/**
 * Menu contextuel pour les conversations dans l'interface Claude
 */
const ConversationContextMenu: React.FC<ConversationContextMenuProps> = ({
  conversationId,
  title,
  isOpen,
  onClose,
  onRename,
  onDelete,
  triggerRef
}) => {
  // État pour stocker la position et l'orientation du menu
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0, right: null as number | null });

  // Mettre à jour la position du menu lorsqu'il s'ouvre
  useEffect(() => {
    console.log("[ConversationContextMenu] isOpen:", isOpen);
    console.log("[ConversationContextMenu] triggerRef.current:", triggerRef.current);

    if (isOpen && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      console.log("[ConversationContextMenu] Button rect:", rect);

      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const menuWidth = 180; // Largeur approximative du menu
      const menuHeight = 120; // Hauteur approximative du menu
      const margin = 5; // Marge de sécurité

      // Position par défaut : à droite du bouton
      let newPosition = {
        top: rect.top + window.scrollY,
        left: rect.right + margin,
        right: null as number | null
      };

      // Vérifier si le menu dépasse du côté droit de l'écran
      if (rect.right + menuWidth + margin > windowWidth) {
        // Positionner à gauche du bouton si le menu dépasse à droite
        newPosition = {
          top: rect.top + window.scrollY,
          left: rect.left - menuWidth - margin,
          right: null
        };

        // Si le menu dépasse également à gauche, le positionner en dessous
        if (rect.left - menuWidth - margin < 0) {
          newPosition = {
            top: rect.bottom + window.scrollY + margin,
            left: rect.left,
            right: null
          };

          // Si le menu dépasse également en bas, le positionner au-dessus
          if (rect.bottom + menuHeight + margin > windowHeight) {
            newPosition = {
              top: rect.top - menuHeight - margin + window.scrollY,
              left: rect.left,
              right: null
            };
          }
        }
      }

      console.log("[ConversationContextMenu] Selected position:", newPosition);
      setMenuPosition(newPosition);
    }
  }, [isOpen, triggerRef]);

  // Gérer le clic en dehors du menu pour le fermer
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("[ConversationContextMenu] Click detected");
      console.log("[ConversationContextMenu] event.target:", event.target);

      // Vérifier si le clic est sur le menu lui-même
      const menuElement = document.querySelector('.conversation-context-menu');
      const isClickInMenu = menuElement?.contains(event.target as Node);

      console.log("[ConversationContextMenu] isClickInMenu:", isClickInMenu);
      console.log("[ConversationContextMenu] triggerRef.current:", triggerRef.current);
      console.log("[ConversationContextMenu] triggerRef.current?.contains:", triggerRef.current?.contains(event.target as Node));

      if (isOpen && !isClickInMenu && !triggerRef.current?.contains(event.target as Node)) {
        console.log("[ConversationContextMenu] Closing menu");
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, triggerRef]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed z-[100] conversation-context-menu"
      style={{
        top: menuPosition.top,
        ...(menuPosition.left !== null ? { left: menuPosition.left } : {}),
        ...(menuPosition.right !== null ? { right: menuPosition.right } : {}),
        pointerEvents: 'auto',
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="min-w-[180px] bg-claude-gray rounded-md p-1.5 shadow-lg border border-claude-light-gray/30 animate-in fade-in-50 duration-150 scale-in-95 origin-top-left"
        style={{
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        {/* Option Renommer */}
        <button
          className="flex items-center w-full px-3 py-2.5 text-sm text-gray-300 hover:bg-claude-light-gray/30 rounded-md cursor-pointer outline-none transition-colors text-left"
          onClick={(e) => {
            e.stopPropagation();
            console.log("[ConversationContextMenu] Rename button clicked");
            console.log("[ConversationContextMenu] conversationId:", conversationId);
            console.log("[ConversationContextMenu] title:", title);
            onRename(conversationId, title);
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Renommer
        </button>

        {/* Séparateur */}
        <div className="h-px bg-claude-light-gray/20 my-1.5 mx-1" />

        {/* Option Supprimer */}
        <button
          className="flex items-center w-full px-3 py-2.5 text-sm text-red-400 hover:bg-claude-light-gray/30 rounded-md cursor-pointer outline-none transition-colors text-left"
          onClick={(e) => {
            e.stopPropagation();
            console.log("[ConversationContextMenu] Delete button clicked");
            console.log("[ConversationContextMenu] conversationId:", conversationId);
            onDelete(conversationId);
            onClose();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Supprimer
        </button>
      </div>
    </div>
  );
};

export default ConversationContextMenu;
