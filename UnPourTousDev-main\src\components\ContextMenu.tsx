import React, { useRef, useEffect } from "react";

interface ContextMenuProps {
  isOpen: boolean;
  onClose: () => void;
  position: { x: number, y: number, openUpward: boolean };
  children: React.ReactNode;
  width?: string;
  className?: string;
}

export function calculateMenuPosition(rect: DOMRect, menuHeight: number = 150, menuWidth: number = 160, isCollapsed: boolean = false) {
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;
  const spaceBelow = windowHeight - rect.bottom;
  const spaceAbove = rect.top;

  // Calculer la position X - priorité à droite du bouton
  let xPosition = rect.right + 5; // 5px de marge du bouton
  const spaceRight = windowWidth - rect.right;

  console.log('Menu positioning debug:', {
    buttonRect: { left: rect.left, right: rect.right, width: rect.width },
    windowWidth,
    spaceRight,
    menuWidth,
    requiredSpace: menuWidth + 15,
    isCollapsed: isCollapsed
  });

  // Logique simplifiée basée sur l'état réel de la sidebar
  if (isCollapsed) {
    // Sidebar collapsed : TOUJOURS à droite
    xPosition = rect.right + 10;
    console.log('🔥 SIDEBAR COLLAPSED - FORCING menu to RIGHT:', xPosition, 'Button right:', rect.right);
  } else {
    // Sidebar extended : logique normale
    if (spaceRight >= menuWidth + 15) {
      xPosition = rect.right + 5;
      console.log('Sidebar extended - positioning menu to the right:', xPosition);
    } else {
      const spaceLeft = rect.left;
      if (spaceLeft >= menuWidth + 15) {
        xPosition = rect.left - menuWidth - 5;
        console.log('Sidebar extended - positioning menu to the left:', xPosition);
      } else {
        xPosition = windowWidth - menuWidth - 10;
        console.log('Sidebar extended - positioning menu at edge:', xPosition);
      }
    }
  }

  // Pour sidebar collapsed, ne pas limiter la position
  if (!isCollapsed) {
    xPosition = Math.max(10, Math.min(xPosition, windowWidth - menuWidth - 10));
  }
  console.log('🎯 Final menu position:', xPosition, 'isCollapsed:', isCollapsed);

  // Si l'espace en dessous est suffisant, ouvre vers le bas
  // Sinon, si l'espace au-dessus est suffisant, ouvre vers le haut
  // Sinon, ouvre vers le bas mais ajuste la position pour éviter de dépasser
  if (spaceBelow >= menuHeight) {
    return {
      x: xPosition,
      y: rect.top,
      openUpward: false
    };
  } else if (spaceAbove >= menuHeight) {
    return {
      x: xPosition,
      y: rect.bottom,
      openUpward: true
    };
  } else {
    return {
      x: xPosition,
      y: Math.max(rect.top, windowHeight - menuHeight),
      openUpward: false
    };
  }
}

export default function ContextMenu({
  isOpen,
  onClose,
  position,
  children,
  width = "w-40",
  className = ""
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  // Ferme le menu si on clique en dehors
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Vérifie si le clic est en dehors du menu
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        console.log("Click outside menu detected");
        // Petit délai pour éviter les conflits avec d'autres gestionnaires d'événements
        setTimeout(() => {
          onClose();
        }, 50);
      }
    }

    if (isOpen) {
      console.log("Adding click outside listener");
      // Utilise mousedown pour capturer le clic avant d'autres gestionnaires
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      console.log("Removing click outside listener");
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // Fonction pour empêcher la propagation des clics
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // Empêche tout comportement par défaut
  };

  // Log pour déboguer le positionnement final
  console.log('🖼️ ContextMenu rendering with position:', {
    x: position.x,
    y: position.y,
    openUpward: position.openUpward,
    finalLeft: `${position.x}px`,
    finalTop: position.openUpward ? `${window.innerHeight - position.y}px` : `${position.y}px`
  });

  return (
    <div
      ref={menuRef}
      className={`fixed bg-white dark:bg-gray-800 rounded-md shadow-lg z-[100] border border-gray-200 dark:border-gray-700 ${width} ${className}`}
      style={{
        [position.openUpward ? 'bottom' : 'top']: `${position.openUpward ? window.innerHeight - position.y : position.y}px`,
        left: `${position.x}px`,
      }}
      onClick={handleClick}
      onMouseDown={handleClick} // Capture également les événements mousedown
    >
      {children}
    </div>
  );
}
