import React, { useEffect, useRef } from 'react';
import katex from 'katex';

interface KatexRendererProps {
  formula: string;
  displayMode?: boolean;
}

/**
 * Composant pour le rendu direct des formules LaTeX avec KaTeX
 * Peut être utilisé comme alternative au rendu via rehype-katex
 */
const KatexRenderer: React.FC<KatexRendererProps> = ({ formula, displayMode = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      try {
        katex.render(formula, containerRef.current, {
          displayMode,
          throwOnError: false,
          strict: 'ignore',
          output: 'htmlAndMathml',
          trust: true,
          errorColor: 'transparent'
        });
      } catch (error) {
        console.error('KaTeX rendering error:', error);
        if (containerRef.current) {
          containerRef.current.textContent = `[Erreur de rendu: ${formula}]`;
          containerRef.current.classList.add('katex-error');
        }
      }
    }
  }, [formula, displayMode]);

  return <div ref={containerRef} className={displayMode ? 'katex-display' : 'katex-inline'} />;
};

export default KatexRenderer;
