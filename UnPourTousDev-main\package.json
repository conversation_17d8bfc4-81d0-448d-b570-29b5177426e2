{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "convex dev --once && node setup.mjs && npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "build": "vite build", "start": "serve -s dist", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@clerk/clerk-react": "^5.30.0", "@clerk/themes": "^2.2.36", "@convex-dev/auth": "^0.0.80", "@heroicons/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.1", "framer-motion": "^12.9.4", "katex": "^0.16.22", "lucide-react": "^0.503.0", "marked": "^15.0.11", "mermaid": "^11.6.0", "notdiamond": "^1.1.2", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-mermaid": "^3.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "semantic-router": "^1.0.0", "serve": "^14.2.1", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "babel-plugin-react-compiler": "^19.1.0-rc.1", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}