import React from 'react';
import { Id } from '../../../../../../convex/_generated/dataModel';

interface DeleteConfirmationProps {
  conversationId: Id<"conversations">;
  visualState: boolean;
  handleCancelDelete: () => void;
  handleDelete: (conversationId: Id<"conversations">) => void;
}

const DeleteConfirmation: React.FC<DeleteConfirmationProps> = ({
  conversationId,
  visualState,
  handleCancelDelete,
  handleDelete
}) => {
  return (
    <div 
      className={`${
        visualState ? "hidden" : "w-full p-3"
      } mb-2 rounded-md bg-claude-light-gray/20 border border-claude-light-gray/10 text-white`}
      onClick={(e) => e.stopPropagation()}
    >
      <p className="text-sm text-red-400 mb-3 font-medium">Supprimer cette conversation ?</p>
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="px-3 py-1.5 text-xs text-gray-400 hover:text-white transition-colors"
          onClick={handleCancelDelete}
        >
          Annuler
        </button>
        <button
          type="button"
          className="px-3 py-1.5 text-xs bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          onClick={() => handleDelete(conversationId)}
        >
          Supprimer
        </button>
      </div>
    </div>
  );
};

export default DeleteConfirmation;
