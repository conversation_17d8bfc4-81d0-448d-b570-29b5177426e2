import React from "react";
import { ActionButtonsProps } from "../types";
import SendButton from "../../../../common/SendButton";

/**
 * Composant ActionButtons pour l'interface Claude
 * Contient uniquement le bouton d'envoi (le bouton AutoRouter a été déplacé)
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  userMessage,
  isLoading,
  startChat
}) => {
  return (
    <div className="flex items-center">
      {/* Bouton d'envoi avec le composant SendButton */}
      <SendButton
        onClick={() => startChat()}
        disabled={!userMessage.trim() || isLoading}
        size="sm"
      >
        {isLoading && (
          <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
      </SendButton>
    </div>
  );
};

export default ActionButtons;
