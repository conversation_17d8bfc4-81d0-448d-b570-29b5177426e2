import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

interface MessageInputProps {
  conversationId: Id<"conversations"> | null;
  onNewConversation: (id: Id<"conversations"> | null | "new") => void;
  isNewConversation?: boolean;
}

export default function MessageInput({ conversationId, onNewConversation, isNewConversation = false }: MessageInputProps) {
  const [message, setMessage] = useState("");
  const [selectedModel, setSelectedModel] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const sendMessage = useMutation(api.messages.send);

  // Récupérer les messages de la conversation
  const messages = useQuery(
    api.messages.list,
    conversationId ? { conversationId } : "skip"
  ) || [];

  // Récupérer la conversation pour vérifier si elle utilise AutoRouter
  const conversation = useQuery(
    api.conversations.getById, 
    conversationId ? { conversationId } : "skip"
  );

  // Récupérer le dernier modèle utilisé dans la conversation
  useEffect(() => {
    if (conversationId && messages.length > 0) {
      // Si la conversation n'utilise pas AutoRouter, on utilise le dernier modèle
      if (conversation && !conversation.usesAutoRouter) {
        // Trouver le dernier message (utilisateur ou assistant) qui contient un modèle
        const lastMessageWithModel = [...messages].reverse().find(msg => msg.modelUsed);

        if (lastMessageWithModel && lastMessageWithModel.modelUsed) {
          setSelectedModel(lastMessageWithModel.modelUsed);
        }
      } else {
        // Si la conversation utilise AutoRouter, on sélectionne "openrouter/auto"
        setSelectedModel("openrouter/auto");
      }
    }
  }, [conversationId, messages, conversation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !selectedModel || isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Si c'est une nouvelle conversation temporaire ou pas de conversation sélectionnée
      const newConversationId = await sendMessage({
        conversationId: (isNewConversation || !conversationId) ? undefined : conversationId,
        content: message,
        modelId: selectedModel,
      });

      setMessage("");

      // Réinitialiser la hauteur du textarea à sa valeur par défaut
      const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
      if (textarea) {
        textarea.style.height = '44px'; // Hauteur minimale définie dans le CSS
        textarea.style.overflowY = 'hidden'; // Masquer la barre de défilement
      }

      // Si une nouvelle conversation a été créée, mettre à jour l'ID de conversation
      if (newConversationId) {
        onNewConversation(newConversationId);
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Gérer la soumission avec Shift+Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (message.trim() && selectedModel && !isSubmitting) {
        handleSubmit(e);
      }
    }
  };

  return (
    <div className="p-4 bg-surface-light dark:bg-surface-dark transition-colors duration-200">
      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="flex items-end space-x-2 justify-end">
          <div className="w-[95%] relative">
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Tapez votre message... (Shift+Enter pour nouvelle ligne)"
              className="w-full p-3 min-h-[44px] max-h-[300px] border border-gray-300 dark:border-border-dark rounded-lg bg-surface-light dark:bg-surface-dark text-text-light dark:text-text-dark placeholder-gray-500 dark:placeholder-tango-300 transition-colors duration-200 focus:outline-none focus:border-fire-700 focus:border-2 resize-none"
              style={{
                height: 'auto',
                overflowY: message.split('\n').length > 11 ? 'auto' : 'hidden',
                wordWrap: 'break-word',
                whiteSpace: 'pre-wrap'
              }}
              rows={1}
              required
              onInput={(e) => {
                // Ajuster automatiquement la hauteur
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                const newHeight = Math.min(target.scrollHeight, 300);
                target.style.height = `${newHeight}px`;

                // Afficher la barre de défilement uniquement si plus de 11 lignes
                const lineCount = target.value.split('\n').length;
                target.style.overflowY = lineCount > 11 ? 'auto' : 'hidden';
              }}
            />
          </div>
          <button
            type="submit"
            disabled={!message.trim() || !selectedModel || isSubmitting}
            className="px-4 py-3 bg-primary dark:bg-primary hover:bg-primary-hover dark:hover:bg-primary-light text-white rounded-lg disabled:opacity-50 transition-colors duration-200 flex-shrink-0"
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Envoi...
              </span>
            ) : (
              "Envoyer"
            )}
          </button>
        </form>
      </div>
    </div>
  );
}




