import React from "react";
import { useNavigate } from "react-router-dom";
import useInterfacePreference from "../../../hooks/useInterfacePreference";
import logo from "../../../assets/logo3.svg";

/**
 * Composant pour sélectionner l'interface préférée
 */
const InterfaceSelector: React.FC = () => {
  const navigate = useNavigate();
  const { setInterfaceType, isLoading } = useInterfacePreference();

  /**
   * Gère la sélection d'une interface
   * @param {string} type - Le type d'interface sélectionné
   */
  const handleSelectInterface = async (type: string) => {
    await setInterfaceType(type);

    // Rediriger vers l'interface sélectionnée
    if (type === "claude") {
      navigate("/claude");
    } else {
      navigate("/original");
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-claude-dark text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-claude-dark text-white flex flex-col items-center justify-center p-4">
      <img src={logo} alt="UnPourTous Logo" className="w-24 h-24 mb-8" />
      <h1 className="text-3xl font-bold mb-8">Choisissez votre interface</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl w-full">
        {/* Interface Claude */}
        <div
          className="bg-claude-gray rounded-xl p-6 cursor-pointer hover:bg-claude-light-gray/30 transition-colors"
          onClick={() => handleSelectInterface("claude")}
        >
          <h2 className="text-xl font-bold mb-4">Interface Claude</h2>
          <p className="mb-4">Interface moderne inspirée de Claude.ai avec une sidebar rétractable et une zone de chat centrée.</p>
          <div className="aspect-video bg-claude-dark/50 rounded-lg flex items-center justify-center overflow-hidden">
            <div className="flex flex-col items-center justify-center">
              <img src={logo} alt="Logo UnPourTous" className="w-16 h-16 mb-2" />
              <span className="text-gray-400 text-sm">Interface Claude</span>
            </div>
          </div>
        </div>

        {/* Interface Originale */}
        <div
          className="bg-claude-gray rounded-xl p-6 cursor-pointer hover:bg-claude-light-gray/30 transition-colors"
          onClick={() => handleSelectInterface("original")}
        >
          <h2 className="text-xl font-bold mb-4">Interface Originale</h2>
          <p className="mb-4">Interface classique avec une sidebar fixe et une disposition traditionnelle.</p>
          <div className="aspect-video bg-claude-dark/50 rounded-lg flex items-center justify-center overflow-hidden">
            <div className="flex flex-col items-center justify-center">
              <img src={logo} alt="Logo UnPourTous" className="w-16 h-16 mb-2" />
              <span className="text-gray-400 text-sm">Interface Originale</span>
            </div>
          </div>
        </div>
      </div>

      <p className="mt-8 text-gray-400">Vous pourrez changer d'interface à tout moment dans les paramètres.</p>
    </div>
  );
};

export default InterfaceSelector;
