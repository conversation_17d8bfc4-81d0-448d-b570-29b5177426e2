export default function AiMessage({ message, conversation }: AiMessageProps) {
  // ...
  
  // Détermine si on doit afficher AutoRouter ou le modèle réel
  let displayModelName = message.modelName || message.modelUsed;
  let displayModelId = message.modelUsed;
  
  // Si la conversation utilise AutoRouter, on affiche toujours AutoRouter
  if (conversation?.usesAutoRouter) {
    displayModelName = "AutoRouter";
    displayModelId = "openrouter/auto";
  }
  
  return (
    <div>
      {/* ... */}
      <div className="text-xs text-gray-500 flex items-center">
        <ModelIcon model={displayModelId} provider="openrouter" size={16} className="mr-1" />
        {displayModelName}
      </div>
      {/* ... */}
    </div>
  );
}