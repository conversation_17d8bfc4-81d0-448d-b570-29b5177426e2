import React from "react";
import { UserMessageProps } from "./types";
import MarkdownRenderer from "../../../common/MarkdownRenderer";
import CopyButton from "../../../common/CopyButton";

/**
 * Composant UserMessage pour afficher les messages de l'utilisateur
 */
const UserMessage: React.FC<UserMessageProps> = ({ message }) => {
  return (
    <div className="bg-claude-light-gray/20 rounded-xl p-4 relative group">
      <div className="font-medium mb-1 text-gray-400 font-body">Vous</div>
      <div className="text-white font-body">
        <MarkdownRenderer content={message.content} />
      </div>

      {/* Copy button for entire user message - positioned at bottom right */}
      <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
        <CopyButton
          text={message.content}
          variant="message"
          size="md"
        />
      </div>
    </div>
  );
};

export default UserMessage;
