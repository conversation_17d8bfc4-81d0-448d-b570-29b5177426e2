/**
 * Utilitaire pour nettoyer les erreurs Mermaid qui pourraient apparaître en bas de la page
 */

// Fonction pour supprimer les éléments d'erreur Mermaid
export function cleanupMermaidErrors() {
  // Sélectionner tous les éléments qui pourraient être des erreurs Mermaid
  const errorSelectors = [
    'div[id^="d3-error-"]',
    'div.error-container',
    'div.error-message',
    'div.error',
    'div[class*="error"]',
    'div[id*="error"]',
    'g[class*="error"]',
    'g[id*="error"]',
    'text:has(tspan:contains("error"))',
    'text:has(tspan:contains("Error"))'
  ];
  
  // Sélectionner les éléments qui sont directement attachés au body
  // et qui ne font pas partie de l'application
  const bodyChildren = document.body.children;
  for (let i = 0; i < bodyChildren.length; i++) {
    const child = bodyChildren[i];
    
    // Vérifier si c'est un élément qui n'appartient pas à l'application
    if (
      child.id !== 'root' && 
      !child.classList.contains('app') &&
      child.tagName !== 'SCRIPT' &&
      child.tagName !== 'LINK' &&
      child.tagName !== 'STYLE'
    ) {
      // Vérifier si c'est probablement un élément d'erreur Mermaid
      if (
        child.innerHTML.includes('error') ||
        child.innerHTML.includes('Error') ||
        child.id.includes('error') ||
        child.id.includes('Error') ||
        (child.className && (
          child.className.includes('error') ||
          child.className.includes('Error')
        ))
      ) {
        // Supprimer l'élément
        child.remove();
      }
    }
  }
  
  // Supprimer les éléments correspondant aux sélecteurs
  errorSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    } catch (e) {
      // Ignorer les erreurs de sélecteur invalide
      console.debug('Sélecteur invalide:', selector);
    }
  });
}

// Fonction pour observer les mutations du DOM et supprimer les erreurs
export function setupMermaidErrorObserver() {
  // Créer un observateur de mutations
  const observer = new MutationObserver((mutations) => {
    let shouldCleanup = false;
    
    // Vérifier si des éléments ont été ajoutés au body
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldCleanup = true;
      }
    });
    
    // Nettoyer les erreurs si nécessaire
    if (shouldCleanup) {
      cleanupMermaidErrors();
    }
  });
  
  // Observer les changements dans le body
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Nettoyer les erreurs existantes
  cleanupMermaidErrors();
  
  // Retourner une fonction pour arrêter l'observation
  return () => observer.disconnect();
}

// Exporter une fonction pour initialiser le nettoyage
export function initMermaidErrorCleaner() {
  // Nettoyer immédiatement
  cleanupMermaidErrors();
  
  // Nettoyer après un court délai (pour les erreurs qui apparaissent après le chargement)
  setTimeout(cleanupMermaidErrors, 500);
  setTimeout(cleanupMermaidErrors, 1000);
  setTimeout(cleanupMermaidErrors, 2000);
  
  // Configurer l'observateur
  return setupMermaidErrorObserver();
}

export default initMermaidErrorCleaner;
