import { AIModel } from "./types";

/**
 * Obtient le nom propre d'un modèle
 * @param model Le modèle d'IA
 * @returns Le nom nettoyé du modèle
 */
export const getCleanModelName = (model: AIModel): string => {
  return model.name;
};

/**
 * Détermine la catégorie principale d'un modèle
 * @param model Le modèle d'IA
 * @returns La catégorie principale du modèle
 */
export const getModelCategory = (model: AIModel): string => {
  if (model.webSearch) return 'web_search';
  if (model.reasoning) return 'reasoning';
  if (model.chat) return 'chat';
  return 'chat'; // Par défaut
};

/**
 * Ajuste la hauteur d'un textarea en fonction de son contenu
 * @param textarea L'élément textarea à ajuster
 */
export const adjustTextareaHeight = (textarea: HTMLTextAreaElement): void => {
  textarea.style.height = 'auto';
  const newHeight = Math.min(textarea.scrollHeight, 300);
  textarea.style.height = `${newHeight}px`;

  // Afficher la barre de défilement uniquement si plus de 11 lignes
  const lineCount = textarea.value.split('\n').length;
  textarea.style.overflowY = lineCount > 11 ? 'auto' : 'hidden';
};
