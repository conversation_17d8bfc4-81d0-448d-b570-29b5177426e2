import React, { ReactNode } from "react";

interface SpotlightBoxProps {
  children: ReactNode;
  className?: string;
}

/**
 * Composant SpotlightBox avec effet de spotlight statique
 */
const SpotlightBox: React.FC<SpotlightBoxProps> = ({ children, className = "" }) => {
  return (
    <div className={`relative max-w-2xl w-full overflow-hidden rounded-xl border border-tango-300 dark:border-bronze-600 bg-white dark:bg-bronze-800 px-4 py-4 shadow-xl transition-colors duration-200 ${className}`}>
      {/* Effet de spotlight statique */}
      <div className="pointer-events-none absolute -inset-px rounded-xl bg-gradient-to-br from-tango-200/50 via-transparent to-fire-200/50 dark:from-tango-800/30 dark:via-transparent dark:to-fire-800/30"></div>
      
      {/* Contenu */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export default SpotlightBox;