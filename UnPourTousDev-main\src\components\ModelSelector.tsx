import { useState } from "react";
import ModelIcon from "./ModelIcon";

interface ModelSelectorProps {
  models: any[];
  selectedModelId: string | null;
  onSelectModel: (modelId: string) => void;
}

export default function ModelSelector({ 
  models, 
  selectedModelId, 
  onSelectModel 
}: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Trouver le modèle sélectionné
  const selectedModel = models.find(model => model.modelId === selectedModelId);
  
  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none"
      >
        {selectedModel && (
          <ModelIcon 
            model={selectedModel.modelId} 
            provider={selectedModel.provider} 
            size={20} 
            className="mr-1"
          />
        )}
        <span>Modèle: {selectedModel?.name || "Chargement..."}</span>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute z-10 mt-1 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg">
          <ul className="py-1 max-h-60 overflow-auto">
            {models.map((model) => (
              <li key={model.modelId}>
                <button
                  onClick={() => {
                    onSelectModel(model.modelId);
                    setIsOpen(false);
                  }}
                  className={`block w-full text-left px-4 py-2 text-sm ${
                    model.modelId === selectedModelId
                      ? "bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100"
                      : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                >
                  <div className="flex items-center">
                    <ModelIcon 
                      model={model.modelId} 
                      provider={model.provider} 
                      size={20} 
                      className="mr-2"
                    />
                    <div className="font-medium">{model.name}</div>
                  </div>
                  {model.description && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                      {model.description}
                    </div>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
