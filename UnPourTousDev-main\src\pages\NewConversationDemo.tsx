import React, { useState, useEffect, useRef } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useNavigate } from "react-router-dom";
import { useTheme } from "../contexts/ThemeContext";
import DemoNavigation from "../components/common/DemoNavigation";

/**
 * Interface for the NewConversationDemo component props
 */
interface NewConversationDemoProps {}

/**
 * Demo page for the new conversation interface inspired by Claude.ai
 */
const NewConversationDemo: React.FC<NewConversationDemoProps> = () => {
  // State
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showModelSelection, setShowModelSelection] = useState(false);
  const [selectedModel, setSelectedModel] = useState("Claude 3.7 Sonnet");
  const [chatStarted, setChatStarted] = useState(false);
  const [aiResponse, setAiResponse] = useState("");
  
  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  
  // Hooks
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();
  
  // Convex mutation for sending messages
  const sendMessage = useMutation(api.messages.send);

  // Adjust textarea height on input
  useEffect(() => {
    if (textareaRef.current) {
      adjustTextareaHeight(textareaRef.current);
    }
  }, [message]);

  // Show model selection when user starts typing
  useEffect(() => {
    setShowModelSelection(message.trim().length > 0);
  }, [message]);

  // Scroll to bottom when AI responds
  useEffect(() => {
    if (bottomRef.current && chatStarted) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [aiResponse, chatStarted]);

  // Function to adjust textarea height
  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = "auto";
    const newHeight = Math.min(textarea.scrollHeight, 300);
    textarea.style.height = `${newHeight}px`;
  };

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || isSubmitting) return;
    
    const userMessage = message.trim();
    setIsSubmitting(true);
    
    try {
      // Start chat UI
      setChatStarted(true);
      
      // Clear input
      setMessage("");
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
      
      // Simulate sending to backend
      // In a real implementation, this would create a new conversation
      // const newConversationId = await sendMessage({
      //   conversationId: undefined, // No conversationId = new conversation
      //   content: userMessage,
      //   modelId: selectedModel,
      // });
      
      // Simulate AI response after delay
      setTimeout(() => {
        setAiResponse("Hello! I'm your AI assistant. How can I help you today?");
        setIsSubmitting(false);
      }, 2000);
      
    } catch (error) {
      console.error("Error sending message:", error);
      setIsSubmitting(false);
    }
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (message.trim() && !isSubmitting) {
        handleSubmit(e);
      }
    }
  };

  return (
    <div className={`new-conversation-demo h-screen flex flex-col overflow-hidden ${isDarkMode ? "dark bg-claude-dark text-gray-200" : "bg-white text-gray-800"}`}>
      <DemoNavigation />
      <div className="flex flex-1 overflow-hidden mt-10">
        {/* Left sidebar */}
        <div className="w-16 border-r border-claude-light-gray flex flex-col items-center py-4 hidden sm:flex">
        <button className="w-8 h-8 mb-6 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>

        <button className="w-8 h-8 bg-claude-orange text-white rounded-full flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
        </button>

        <button className="w-8 h-8 text-gray-400 rounded-full flex items-center justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
          </svg>
        </button>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {!chatStarted ? (
          <>
            {/* Subscription info */}
            <div className="flex justify-center mt-4">
              <div className="px-4 py-1.5 bg-claude-gray rounded-lg flex items-center gap-2">
                <span className="text-sm">Free Plan</span>
                <span className="text-primary text-sm">•</span>
                <a href="#" className="text-primary text-sm">Upgrade</a>
              </div>
            </div>

            {/* Main content area with centered elements */}
            <div className="flex flex-col flex-1 justify-center items-center px-4">
              <div className="flex flex-col items-center w-full max-w-3xl">
                {/* Empty chat state */}
                <div className="flex flex-col items-center justify-center mb-8">
                  <div className="flex justify-center mb-4">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-claude-orange">
                      <path d="M12 17V17.5V18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                      <path d="M12 14L12 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                      <path d="M8.5 9.5L12 6L15.5 9.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5"/>
                    </svg>
                  </div>
                  <h1 className="text-4xl font-light text-center mb-2">How was your day?</h1>
                  <p className="text-gray-400 text-center text-lg">I'm here to chat, help, or just listen</p>
                </div>

                {/* Input area - centered on page */}
                <div className="w-full">
                  <form onSubmit={handleSubmit}>
                    <div className="relative w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
                      <textarea 
                        ref={textareaRef}
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60"
                        placeholder="How can I help you?"
                        rows={1}
                      />
                      <div className="flex justify-between px-2 pt-1">
                        <div className="flex items-center">
                          <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                          </button>
                          <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                          </button>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className={`text-sm text-gray-400 ${showModelSelection ? "" : "hidden"}`}>
                            <select 
                              className="bg-claude-gray border-none text-white cursor-pointer p-1 rounded"
                              value={selectedModel}
                              onChange={(e) => setSelectedModel(e.target.value)}
                            >
                              <option>Claude 3.7 Sonnet</option>
                              <option>Claude 3 Haiku</option>
                              <option>Claude 3 Opus</option>
                            </select>
                          </div>
                          {/* Auto Router Button */}
                          <button type="button" className="p-1.5 rounded-lg text-white bg-claude-orange opacity-80 hover:opacity-100">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                            </svg>
                          </button>
                          <button 
                            type="submit" 
                            disabled={!message.trim() || isSubmitting}
                            className="p-1.5 text-white rounded-lg bg-primary opacity-80 hover:opacity-100 disabled:opacity-50"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>

                  {/* Categories */}
                  <div className="flex justify-center mt-4 flex-wrap gap-2">
                    <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
                      </svg>
                      Chat
                    </button>
                    <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                      </svg>
                      Web Search
                    </button>
                    <button className="px-4 py-2 rounded-lg bg-claude-gray text-sm flex items-center gap-2 hover:bg-claude-light-gray/70">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
                      </svg>
                      Reasoning
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Chat started UI */}
            <div className="flex-1 overflow-y-auto py-4 px-4">
              <div className="max-w-3xl mx-auto">
                <div className="mb-6">
                  <div className="font-medium mb-1 text-gray-400">You</div>
                  <div className="text-white">{message || "How can I help you?"}</div>
                </div>
                <div>
                  <div className="font-medium mb-1 text-gray-400">Assistant</div>
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <span className="loader"></span>
                    </div>
                  ) : (
                    <div className="text-white">{aiResponse}</div>
                  )}
                </div>
                <div ref={bottomRef}></div>
              </div>
            </div>
            <div className="px-4 pb-4">
              <form onSubmit={handleSubmit}>
                <div className="relative max-w-3xl mx-auto w-full rounded-xl border border-claude-light-gray bg-claude-gray p-2 shadow-sm">
                  <textarea 
                    ref={textareaRef}
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="w-full bg-transparent outline-none resize-none text-base py-2 px-3 h-12 max-h-60"
                    placeholder="How can I help you?"
                    rows={1}
                  />
                  <div className="flex justify-between px-2 pt-1">
                    <div className="flex items-center">
                      <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                      </button>
                      <button type="button" className="p-1.5 rounded-lg text-gray-400 hover:bg-claude-light-gray/30 hover:text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                        </svg>
                      </button>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className={`text-sm text-gray-400 ${showModelSelection ? "" : "hidden"}`}>
                        <select 
                          className="bg-claude-gray border-none text-white cursor-pointer p-1 rounded"
                          value={selectedModel}
                          onChange={(e) => setSelectedModel(e.target.value)}
                        >
                          <option>Claude 3.7 Sonnet</option>
                          <option>Claude 3 Haiku</option>
                          <option>Claude 3 Opus</option>
                        </select>
                      </div>
                      {/* Auto Router Button */}
                      <button type="button" className="p-1.5 rounded-lg text-white bg-claude-orange opacity-80 hover:opacity-100">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                        </svg>
                      </button>
                      <button 
                        type="submit" 
                        disabled={!message.trim() || isSubmitting}
                        className="p-1.5 text-white rounded-lg bg-primary opacity-80 hover:opacity-100 disabled:opacity-50"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </>
        )}
      </div>
      </div>
    </div>
  );
};

export default NewConversationDemo;
