import React from 'react';
import { Id } from '../../../../../../convex/_generated/dataModel';

interface RenameFormProps {
  conversationId: Id<"conversations">;
  newTitle: string;
  visualState: boolean;
  renameInputRef: React.RefObject<HTMLInputElement>;
  handleRename: (e: React.FormEvent, conversationId: Id<"conversations">) => void;
  setNewTitle: (title: string) => void;
  handleCancelRename: () => void;
}

const RenameForm: React.FC<RenameFormProps> = ({
  conversationId,
  newTitle,
  visualState,
  renameInputRef,
  handleRename,
  setNewTitle,
  handleCancelRename
}) => {
  return (
    <form 
      onSubmit={(e) => handleRename(e, conversationId)}
      className={`${
        visualState ? "hidden" : "w-full p-3"
      } mb-2 rounded-md bg-claude-light-gray/20 border border-claude-light-gray/10`}
      onClick={(e) => e.stopPropagation()}
    >
      <input
        ref={renameInputRef}
        type="text"
        value={newTitle}
        onChange={(e) => setNewTitle(e.target.value)}
        className="w-full p-2 bg-claude-gray border border-claude-light-gray/30 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-claude-orange/50 focus:border-claude-orange/50"
        autoFocus
        onBlur={(e) => handleRename(e, conversationId)}
        placeholder="Nom de la conversation"
      />
      <div className="flex justify-end mt-3 space-x-3">
        <button
          type="button"
          className="px-3 py-1.5 text-xs text-gray-400 hover:text-white transition-colors"
          onClick={handleCancelRename}
        >
          Annuler
        </button>
        <button
          type="submit"
          className="px-3 py-1.5 text-xs bg-claude-orange text-white rounded-md hover:bg-claude-orange/90 transition-colors"
        >
          Renommer
        </button>
      </div>
    </form>
  );
};

export default RenameForm;
