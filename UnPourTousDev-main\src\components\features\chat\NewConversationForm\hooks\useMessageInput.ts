import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../../../convex/_generated/api";
import { Id } from "../../../../../../convex/_generated/dataModel";

/**
 * Hook personnalisé pour gérer la saisie et l'envoi des messages
 */
export const useMessageInput = (
  onConversationCreated: (id: Id<"conversations">) => void,
  selectedModel: string
) => {
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Mutation Convex pour envoyer un message
  const sendMessage = useMutation(api.messages.send);

  // Fonction pour gérer la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Envoyer le message pour créer une nouvelle conversation
      const newConversationId = await sendMessage({
        conversationId: undefined, // Pas de conversationId = nouvelle conversation
        content: message,
        modelId: selectedModel,
      });

      setMessage("");

      // Réinitialiser la hauteur du textarea à sa valeur par défaut
      const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
      if (textarea) {
        textarea.style.height = '44px'; // Hauteur minimale définie dans le CSS
        textarea.style.overflowY = 'hidden'; // Masquer la barre de défilement
      }

      // Si une nouvelle conversation a été créée, mettre à jour l'ID de conversation
      if (newConversationId) {
        onConversationCreated(newConversationId);
      }
    } catch (error) {
      console.error("Erreur lors de l'envoi du message:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fonction pour ajuster automatiquement la hauteur du textarea
  const adjustTextareaHeight = (e: React.FormEvent<HTMLTextAreaElement>) => {
    const target = e.target as HTMLTextAreaElement;
    target.style.height = 'auto';
    const newHeight = Math.min(target.scrollHeight, 300);
    target.style.height = `${newHeight}px`;

    // Afficher la barre de défilement uniquement si plus de 11 lignes
    const lineCount = target.value.split('\n').length;
    target.style.overflowY = lineCount > 11 ? 'auto' : 'hidden';
  };

  return {
    message,
    setMessage,
    isSubmitting,
    handleSubmit,
    adjustTextareaHeight
  };
};
