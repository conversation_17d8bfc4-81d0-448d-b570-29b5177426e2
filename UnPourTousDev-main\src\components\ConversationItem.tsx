import { ModelIconDisplay } from "./features/models";
import { Conversation } from "../types/Conversation";

interface ConversationItemProps {
  conversation: Conversation & {
    lastModelUsed?: string;
    lastModelProvider?: string;
    usesAutoRouter?: boolean;
  };
}

export default function ConversationItem({ conversation }: ConversationItemProps) {
  return (
    <div>
      <div className="flex items-center">
        <ModelIconDisplay conversation={conversation} size={16} className="mr-2" />
        <span>{conversation.title}</span>
      </div>
    </div>
  );
}