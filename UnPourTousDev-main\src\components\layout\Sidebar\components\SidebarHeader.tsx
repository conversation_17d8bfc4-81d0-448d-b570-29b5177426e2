import React from "react";
import { SidebarHeaderProps } from "../types";
import logo from "../../../../assets/logo2.svg";

/**
 * Composant pour l'en-tête de la sidebar
 */
const SidebarHeader: React.FC<SidebarHeaderProps> = ({ isCollapsed, toggleSidebar }) => {
  return (
    <div className="border-b border-gray-200 dark:border-border-dark bg-surface-light dark:bg-surface-dark p-3">
      {/* En-tête avec bouton de bascule et titre alignés */}
      <div className="flex items-center">
        {/* Bouton pour basculer la sidebar - aligné avec le titre */}
        <button
          onClick={toggleSidebar}
          className="mr-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          title={isCollapsed ? "Développer" : "Rétracter"}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isCollapsed ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            )}
          </svg>
        </button>

        {/* Logo qui disparaît quand la sidebar est rétractée */}
        {!isCollapsed && (
          <img src={logo} alt="UnPourTous Logo" className="h-4 w-auto mr-2" />
        )}
      </div>
    </div>
  );
};

export default SidebarHeader;
