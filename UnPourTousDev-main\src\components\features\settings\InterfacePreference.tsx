import React from "react";
import { useNavigate } from "react-router-dom";
import useInterfacePreference from "../../../hooks/useInterfacePreference";

/**
 * Composant pour gérer les préférences d'interface dans les paramètres
 */
const InterfacePreference: React.FC = () => {
  const navigate = useNavigate();
  const { interfaceType, setInterfaceType, isLoading } = useInterfacePreference();

  /**
   * Gère le changement d'interface
   * @param {string} type - Le nouveau type d'interface
   */
  const handleChangeInterface = async (type: string) => {
    await setInterfaceType(type);
    
    // Rediriger vers l'interface sélectionnée
    if (type === "claude") {
      navigate("/claude");
    } else {
      navigate("/original");
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium text-gray-900 dark:text-white">Interface</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Chargement...
            </p>
          </div>
          <div className="animate-pulse w-32 h-10 bg-gray-200 dark:bg-gray-600 rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="font-medium text-gray-900 dark:text-white">Interface</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Choisissez l'interface que vous préférez utiliser
          </p>
        </div>
        <select
          value={interfaceType || "claude"}
          onChange={(e) => handleChangeInterface(e.target.value)}
          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm"
        >
          <option value="claude">Interface Claude</option>
          <option value="original">Interface Originale</option>
        </select>
      </div>
    </div>
  );
};

export default InterfacePreference;
