import React from "react";
import { CategoryButtonsProps } from "../types";

/**
 * Composant CategoryButtons pour l'interface Claude
 * Affiche les trois catégories principales de modèles:
 * - Chat (pour les modèles de conversation générale)
 * - Recherche Web (pour les modèles avec capacité de recherche)
 * - Raisonnement (pour les modèles optimisés pour la résolution de problèmes)
 */
const CategoryButtons: React.FC<CategoryButtonsProps> = ({
  openCategory,
  toggleCategory
}) => {
  return (
    <div className="flex justify-center mt-4 flex-wrap gap-3 font-body">
      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "chat"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
          flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all duration-200 transform ${openCategory === "chat" ? "scale-105" : ""}`}
        onClick={() => toggleCategory("chat")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
        </svg>
        Discussion
      </button>
      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "web_search"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
          flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all duration-200 transform ${openCategory === "web_search" ? "scale-105" : ""}`}
        onClick={() => toggleCategory("web_search")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
        </svg>
        Recherche Web
      </button>
      <button
        className={`px-4 py-2 rounded-lg ${openCategory === "reasoning"
          ? "bg-primary text-white border-2 border-primary shadow-md"
          : "bg-claude-gray text-white border border-claude-light-gray"}
          flex items-center gap-2 hover:bg-claude-light-gray/70 transition-all duration-200 transform ${openCategory === "reasoning" ? "scale-105" : ""}`}
        onClick={() => toggleCategory("reasoning")}
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
        </svg>
        Raisonnement
      </button>
    </div>
  );
};

export default CategoryButtons;
